import nbDotDieuTriProvider from "data-access/nb-dot-dieu-tri-provider";
import nbPhieuThuProvider from "data-access/nb-phieu-thu-provider";
import { message } from "antd";
import { PAGE_DEFAULT } from "constants/index";
import { combineSort } from "utils";
import goiSoProvider from "data-access/goi-so-provider";
import { t } from "i18next";
import nbHoaDonDienTuProvider from "data-access/nb-hoa-don-dien-tu-provider";
import { cloneDeep, isArray, isEqual } from "lodash";
import nbPhieuDoiTraProvider from "data-access/nb-phieu-doi-tra-provider";
import printProvider from "data-access/print-provider";
import { centralizedErrorHandling } from "lib-utils";

const initData = {
  listData: [],
  chuaThanhToan: 0,
  daThanhToan: 0,
  tongSo: 0,
  choDuyetQrNgoaiTru: 0,
  choDuyetQrNoiTru: 0,
  totalElements: 0,
  page: PAGE_DEFAULT,
  size: 10,
  dataSearch: {},
  dataSortColumn: {},
  dsPhieuThu: [],
  manHinhPath: null,
};

export default {
  state: cloneDeep(initData),
  reducers: {
    updateData(state, payload = {}) {
      return { ...state, ...payload };
    },
    clearData(state, payload = {}) {
      return { ...cloneDeep(initData), ...payload };
    },
  },
  effects: (dispatch) => ({
    getStatistical: (dataSearch) => {
      nbPhieuThuProvider
        .getStatistical(dataSearch)
        .then((s) => {
          dispatch.danhSachPhieuThu.updateData({
            ...s.data,
            totalElements: s.data?.tongSo || 0,
          });
        })
        .catch((e) => {
          message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
        });
    },
    onSizeChange: (
      { size, page, dataSearch = {}, dataSortColumn = {}, isOnly, getThongKe },
      state
    ) => {
      const _dataSearch = {
        ...(state.danhSachPhieuThu.dataSearch || {}),
        ...dataSearch,
      };
      const _dataSortColumn = {
        ...(state.danhSachPhieuThu.dataSortColumn || {}),
        ...dataSortColumn,
      };

      dispatch.danhSachPhieuThu.updateData({
        size,
        page: page || 0,
        dataSearch: _dataSearch,
        dataSortColumn: _dataSortColumn,
      });
      dispatch.danhSachPhieuThu.onSearch({
        page: page || 0,
        size,
        dataSearch: _dataSearch,
        dataSortColumn: _dataSortColumn,
        isOnly,
        getThongKe,
      });
    },
    onSearch: (
      {
        page = 0,
        noUpdateData,
        isOnly = false,
        getThongKe = false,
        ...payload
      },
      state
    ) => {
      let newState = { isLoading: true, page };
      dispatch.danhSachPhieuThu.updateData(newState);
      let size = payload.size || state.danhSachPhieuThu.size;
      const sort = combineSort(
        payload.dataSortColumn || state.danhSachPhieuThu.dataSortColumn || {}
      );
      const nbDotDieuTriId =
        payload.nbDotDieuTriId || state.danhSachPhieuThu?.nbDotDieuTriId;

      let dataSearch = cloneDeep(
        payload.dataSearch || state.danhSachPhieuThu.dataSearch || {}
      );

      if (dataSearch.hasOwnProperty("isTatCaToaNha")) {
        if (dataSearch.isTatCaToaNha) {
          delete dataSearch.nhaThuNganId;
        }
        delete dataSearch.isTatCaToaNha;
      }

      if (getThongKe) dispatch.danhSachPhieuThu.getStatistical(dataSearch);
      return new Promise((resolve, reject) => {
        nbPhieuThuProvider
          .searchTongHop({
            page,
            size,
            sort,
            ...dataSearch,
            nbDotDieuTriId,
            ...(isOnly && {
              tuThoiGianVaoVien: null,
              denThoiGianVaoVien: null,
            }),
          })
          .then((s) => {
            if (!noUpdateData) {
              dispatch.danhSachPhieuThu.updateData({
                listData: (s?.data || []).map((item, index) => {
                  item.index = page * size + index + 1;
                  return item;
                }),
                isLoading: false,
                ...(!!s?.totalElements && { totalElements: s?.totalElements }),
                page,
                first: s?.first,
                last: s?.last,
              });
            }
            resolve(s);
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
            dispatch.danhSachPhieuThu.updateData({
              listData: [],
              isLoading: false,
            });
          });
      });
    },
    onSortChange: ({ getThongKe = false, ...payload }, state) => {
      const dataSortColumn = {
        ...state.danhSachPhieuThu.dataSortColumn,
        ...payload,
      };
      dispatch.danhSachPhieuThu.updateData({
        page: 0,
        dataSortColumn,
      });
      dispatch.danhSachPhieuThu.onSearch({
        page: 0,
        dataSortColumn,
        getThongKe,
      });
    },

    onChangeInputSearch: (
      { isOnly = false, getThongKe, nbDotDieuTriId, ...payload },
      state
    ) => {
      const dataSearch = {
        nbDotDieuTriId: nbDotDieuTriId || state.danhSachPhieuThu.nbDotDieuTriId,
        ...(state.danhSachPhieuThu.dataSearch || {}),
        ...payload,
      };
      dispatch.danhSachPhieuThu.updateData({
        page: 0,
        dataSearch,
      });
      dispatch.danhSachPhieuThu.onSearch({
        page: 0,
        dataSearch,
        isOnly,
        getThongKe,
      });
    },
    getDsPhieuThu: ({ page = 0, size, ...payload }, state) => {
      return new Promise((resolve, reject) => {
        nbPhieuThuProvider
          .searchTongHop({
            page,
            size,
            ...payload,
          })
          .then((s) => {
            if (s?.code === 0) {
              let data = s?.data || [];
              if (!isEqual(data, state.danhSachPhieuThu.dsPhieuThu)) {
                dispatch.danhSachPhieuThu.updateData({
                  dsPhieuThu: data,
                });
              }
              resolve(data);
            } else {
              reject(s);
              message.error(s?.message);
            }
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    exportExcel: (payload, state) => {
      return new Promise((resolve, reject) => {
        nbPhieuThuProvider
          .exportExcel(payload)
          .then((s) => {
            if (s?.code == 0 && s?.data) {
              resolve(s?.data);
            } else {
              reject(s);
            }
          })
          .catch((e) => {
            reject(e);
          });
      });
    },

    goiNbTiepTheo: ({ quayId, ...rest }, state) => {
      return new Promise((resolve, reject) => {
        goiSoProvider
          .postNbTiepTheoThuNgan({ quayId, ...rest })
          .then((s) => {
            if (s?.code == 0 && s?.data) {
              resolve(s?.data?.nbTiepTheo);
            } else {
              reject(s);
            }
          })
          .catch((e) => {
            reject(e);
          });
      });
    },

    getDsNbQms: (quayId, state) => {
      return new Promise((resolve, reject) => {
        goiSoProvider
          .getDsNbQmsThuNgan(quayId)
          .then((s) => {
            if (s?.code == 0 && s?.data) {
              resolve(s?.data);
              dispatch.danhSachPhieuThu.updateData(s?.data);
            } else {
              reject(s);
            }
          })
          .catch((e) => {
            reject(e);
          });
      });
    },

    getNbTiepTheo: (quayId, state) => {
      return new Promise((resolve, reject) => {
        if (!quayId) {
          reject("Không truyền quầy Id");
          return;
        }
        goiSoProvider
          .getNbTiepTheoThuNgan(quayId)
          .then((s) => {
            if (s?.code == 0 && s?.data) {
              resolve(s?.data?.nbTiepTheo);
              dispatch.danhSachPhieuThu.updateData({
                nbTiepTheo: s?.data?.nbTiepTheo,
                nbDangThucHien: s?.data?.nbDangThucHien,
              });
            } else {
              dispatch.danhSachPhieuThu.updateData({
                nbTiepTheo: null,
                nbDangThucHien: null,
              });
              reject(s);
            }
          })
          .catch((e) => {
            reject(null);
          });
      });
    },

    getThongTinNb: (nbDotDieuTriId, state) => {
      return new Promise((resolve, reject) => {
        nbPhieuThuProvider
          .searchTongHop({
            page: "",
            size: "",
            nbDotDieuTriId,
            thanhToan: -1,
          })
          .then((s) => {
            if (s?.code == 0 && s?.data && s.data.length > 0) {
              resolve(s.data[0]);
              dispatch.danhSachPhieuThu.updateData({
                ttNbTiepTheo: s.data[0],
              });
            } else {
              dispatch.danhSachPhieuThu.updateData({
                ttNbTiepTheo: null,
              });
              reject(s);
            }
          })
          .catch((e) => {
            reject(e);
          });
      });
    },
    dongQuay: ({ quayHienTai, quayMoi = "" }, state) => {
      return new Promise((resolve, reject) => {
        const id = state.danhSachPhieuThu?.nbLaySo?.id; //lấy id người bệnh lấy số hiện tại đang tiếp đón, để next bệnh nhân
        if (quayHienTai)
          goiSoProvider
            .dongQuayThuNgan(quayHienTai)
            .then((s) => {
              if (quayMoi) {
                dispatch.danhSachPhieuThu.getNbTiepTheo({
                  id: quayMoi,
                  data: { nbTiepTheoId: id }, //khi đổi quầy thì bỏ qua nb đang tiếp đón
                });
              }
              resolve(s);
            })
            .catch((e) => {
              message.error(e?.message);
              reject(e);
            });
      });
    },
    phatHanhHoaDonHangLoat: (payload) => {
      return new Promise((resolve, reject) => {
        nbHoaDonDienTuProvider
          .phatHanhHoaDonHangLoat(payload)
          .then((s) => {
            if (
              s.code === 0 &&
              (s?.data || []).every((x) => x.trangThai == 20)
            ) {
              resolve(s?.data);
              message.success(t("thuNgan.xuatHoaDonThanhCong"));
            } else {
              if (s.code !== 0) {
                message.error(
                  s?.message || t("common.xayRaLoiVuiLongThuLaiSau")
                );
              } else {
                (
                  (s?.data || []).filter((x) => x.trangThai != 20) || []
                ).forEach((element) => {
                  message.error(
                    element?.phanHoi || t("common.xayRaLoiVuiLongThuLaiSau")
                  );
                });
              }

              reject(s?.data);
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    duyetChuyenChiPhiNoiTru: ({ nbDotDieuTriId, nhaThuNganId, lyDo }) => {
      return new Promise((resolve, reject) => {
        nbPhieuDoiTraProvider
          .duyetChuyenChiPhi({ nbDotDieuTriId, nhaThuNganId, lyDo })
          .then((s) => {
            if (s?.code === 0 || s?.code == 1020) resolve(s);
            else {
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
              reject(s);
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    inPhieuChi: (id, state) => {
      return new Promise((resolve, reject) => {
        nbPhieuDoiTraProvider
          .inPhieuChi(id)
          .then((s) => {
            printProvider.printPdf(s.data);
            resolve(s?.data);
          })
          .catch((e) => {
            reject();
          });
      });
    },
    inPhieuThuDaThanhToan: (id, state) => {
      return new Promise((resolve, reject) => {
        nbPhieuThuProvider
          .getInPhieuThu({
            phieuThuId: id,
          })
          .then((s) => {
            printProvider.printPdf(s.data);
            resolve(s?.data);
          })
          .catch((e) => {
            reject();
          });
      });
    },
    inPhieuChiDoiTra: (id, state) => {
      return new Promise((resolve, reject) => {
        nbPhieuDoiTraProvider
          .inPhieuChi(id)
          .then((s) => {
            printProvider.printPdf(s.data);
            resolve(s?.data);
          })
          .catch((e) => {
            reject();
          });
      });
    },
    huyDuyetChuyenNoiTru: (payload, state) => {
      return new Promise((resolve, reject) => {
        nbPhieuThuProvider
          .huyDuyetChuyenNoiTru(payload)
          .then((s) => {
            if (s?.code == 0) {
              resolve(s?.data);
              message.success(t("thuNgan.huyDuyetChuyenNoiTruThanhCong"));
            } else {
              reject();
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            }
          })
          .catch((e) => {
            reject();
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
  }),
};
