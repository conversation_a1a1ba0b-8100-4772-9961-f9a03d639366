import React from "react";
export const generateTimeArray = () => {
  const times = [];
  const start = new Date(); // hôm nay
  start.setHours(19, 0, 0, 0); // 19:00

  const end = new Date(start);
  end.setDate(end.getDate() + 1); // chuy<PERSON>n sang ngày hôm sau
  end.setHours(7, 0, 0, 0); // 07:00 hôm sau

  const step = 20 * 60 * 1000; // 20 phút = 1200000 ms

  for (let t = start.getTime(); t <= end.getTime(); t += step) {
    const d = new Date(t);
    const hh = String(d.getHours()).padStart(2, "0");
    const mm = String(d.getMinutes()).padStart(2, "0");
    times.push(`${hh}:${mm}`);
  }

  return times;
};

export const DATA_TABLE = [
  {
    label1: "Nhận định",
    rowSpan1: 17,
    label2: (
      <div>
        1. Toàn trạng <i>(Tỉnh/ <PERSON><PERSON> mơ/ Hôn mê/ Khác)</i>
      </div>
    ),
    key: "toanTrang",
    type: "droplist",
    data: [
      { label: "Tỉnh", value: 1 },
      { label: "Lơ mơ", value: 2 },
      { label: "Hôn mê", value: 3 },
      { label: "Khác", value: 4 },
    ],
  },
  // 2
  {
    label2: (
      <div>
        2. Da, niêm mạc <i>(Hồng/ Nhợt/ Tím tái/ Khác)</i>
      </div>
    ),
    key: "daNiemMac",
    type: "droplist",
    data: [
      { label: "Hồng", value: 1 },
      { label: "Nhợt", value: 2 },
      { label: "Tím tái", value: 3 },
      { label: "Khác", value: 4 },
    ],
  },
  // 3
  {
    label2: (
      <div>
        3. Mạch <i>(Lần/phút)</i>
      </div>
    ),
    key: "mach",
    type: "string",
  },
  // 4
  {
    label2: (
      <div>
        4. Nhiệt độ <i>(°C)</i>
      </div>
    ),
    key: "nhietDo",
    type: "string",
  },
  // 5
  {
    label2: (
      <div>
        5. Huyết áp <i>(mmHg)</i>
      </div>
    ),
    key: "huyetAp",
    type: "string",
  },
  // 6
  {
    label2: (
      <div>
        6. Nhịp thở <i>(Lần/phút) (mô tả nếu bất thường)</i>
      </div>
    ),
    key: "nhipTho",
    type: "string",
  },
  // 7
  {
    label2: (
      <div>
        7. Vú <i>(Mô tả thực tế)</i>
      </div>
    ),
    key: "vu",
    type: "string",
  },
  // 8
  {
    label2: (
      <div>
        8. Vết mổ/ Vết chích, bóc/ TSM:{" "}
        <i>(Vị trí, Khối Sưng/ nề/ Thấm máu...)</i>
      </div>
    ),
    key: "vetMoVetChich",
    type: "string",
  },
  // 9
  {
    label2: (
      <div>
        9. Đau bụng <i>(Có, Không, mô tả thực tế)</i>
      </div>
    ),
    key: "dauBung",
    type: "string",
  },
  // 10
  {
    label2: (
      <div>
        10. Bụng chướng <i>(Có, Không, mô tả thực tế)</i>
      </div>
    ),
    key: "bungChuong",
    type: "string",
  },
  // 11
  {
    label2: (
      <div>
        11. Cơ hội tử cung <i>(Cầu an toàn, khác...)</i>
      </div>
    ),
    key: "coHoiTuCung",
    type: "string",
  },
  // 12
  {
    label2: (
      <div>
        12. Ra máu AD/ Sản dịch: <i>(Không/ Có: số lượng, màu sắc, mùi...)</i>
      </div>
    ),
    key: "raMauADSanDich",
    type: "string",
  },
  // 13
  {
    label2: (
      <div>
        13. Dẫn lưu <i>(Vị trí, số lượng, màu sắc...)</i>
      </div>
    ),
    key: "danLuu",
    type: "string",
  },
  // 14
  {
    label2: (
      <div>
        14. Tình trạng thai <i>(TT, CCTC, CTC, ngôi, ối...)</i>
      </div>
    ),
    key: "tinhTrangThai",
    type: "string",
  },
  // 15
  {
    label2: <div>15. Vấn đề khác</div>,
    key: "vanDeKhac",
    type: "string",
  },
  // 16
  {
    label2: <div>16. Nhận định nhu cầu TV - GDSK</div>,
    key: "nhuCauTVGDSK",
    type: "string",
  },
  // 17
  {
    label2: (
      <div>
        17. Phân cấp chăm sóc <i>(Cấp I,II,III)</i>
      </div>
    ),
    key: "phanCapChamSoc",
    type: "droplist",
    data: [
      { label: "Cấp I", value: 1 },
      { label: "Cấp II", value: 2 },
      { label: "Cấp III", value: 3 },
    ],
    mode: "onlyOne",
  },
  { label1: "Chẩn đoán điều dưỡng", colSpan1: 2, key: "chanDoanDieuDuong" },
  {
    label1: "Lập KHCS: Theo dõi sát tình trạng NB",
    colSpan1: 2,
    disable: true,
  },
  {
    label1: "Thực hiện KHCS",
    rowSpan1: 3,
    label2: (
      <div>1. Tư vấn: NQKP, DD, NCBSM, VĐ, DHNH, tái khám, ra viện...</div>
    ),
    key: "tuVan",
    type: "string",
  },
  {
    label2: (
      <div>
        2. Thực hiện y lệnh:{" "}
        <i>(thuốc, thay băng, truyền máu, CTG, thông tiểu...)</i>
      </div>
    ),
    key: "thucHienYLenh",
    type: "string",
  },
  {
    label2: <div>3. Chăm sóc / Hành động khác/ Giao nhận trực</div>,
    key: "chamSocKhac",
    type: "string",
  },
  {
    label1: "Đánh giá",
    rowSpan1: 2,
    label2: <div>Tình trạng người bệnh</div>,
    key: "tinhTrangNguoiBenh",
    type: "string",
  },
  {
    label2: (
      <div>
        Điều dưỡng/ Hộ sinh <i>(Ký và ghi rõ họ tên)</i>
      </div>
    ),
    key: "tinhTrangNguoiBenh",
    type: "sign",
  },
];
