import React, { useEffect, useMemo, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import moment from "moment";
import { checkRole } from "lib-utils/role-utils";
import {
  Checkbox,
  CreatedWrapper,
  Select,
  ListImage,
  DateTimePicker,
  Tooltip,
  DatePicker,
  Button,
} from "components";
import { BIRTHDAY_FORMAT, ENUM, PAGE_DEFAULT, ROLES, THIET_LAP_CHUNG } from "constants/index";
import { Input, Form, Row, Col } from "antd";
import { openInNewTab, resizeImage } from "utils";
import { EyeInvisibleOutlined, EyeTwoTone } from "@ant-design/icons";
import TextArea from "antd/lib/input/TextArea";
import { useEnum, useListAll, useLoading, useStore, useThietLap } from "hooks";
import { Main } from "./styled";
import { cloneDeep } from "lodash";
import { SVG } from "assets";
import ModalPatientSign from "pages/editor/report/components/ModalPatientSign";
import fileUtils from "utils/file-utils";
import stringUtils from "mainam-react-native-string-utils";
import fileProvider from "data-access/file-provider";

const ThongTinChiTiet = React.forwardRef(
  ({ layerId, getListNhanVien }, ref) => {
    const refAutoFocus = useRef(null);
    const refModalSign = useRef(null);

    const { t } = useTranslation();
    const { showLoading, hideLoading } = useLoading();
    const [dataDOI_MAT_KHAU_KY_SO] = useThietLap(
      THIET_LAP_CHUNG.DOI_MAT_KHAU_KY_SO
    );

    const [form] = Form.useForm();
    const [logo, setLogo] = useState();
    const [anhKy, setAnhKy] = useState();
    const dataEditDefault = useStore("nhanVien.dataEditDefault");
    const dataBanSao = useStore("nhanVien.dataBanSao", {});

    const editStatus = !!dataEditDefault?.id;

    const tuThoiGianKhoa = Form.useWatch("tuThoiGianKhoa", form);
    const denThoiGianKhoa = Form.useWatch("denThoiGianKhoa", form);
    const taiKhoan2 = Form.useWatch("taiKhoan2", form);
    const [listGioiTinh] = useEnum(ENUM.GIOI_TINH);
    const { listAllVanBang } = useSelector((state) => state.vanBang);
    const { listAllChuyenKhoa } = useSelector((state) => state.chuyenKhoa);
    const { listAllHocHamHocVi } = useSelector((state) => state.hocHamHocVi);
    const { listAllChucVu } = useSelector((state) => state.chucVu);
    const [listAllChiNhanh] = useListAll("chiNhanh", {}, true);

    const {
      nhanVien: { createOrEdit, postTTKhoaPhong, updateData },
      chucVu: { getListAllChucVu },
      hocHamHocVi: { getListAllHocHamHocVi },
      chuyenKhoa: { getListAllChuyenKhoa },
      vanBang: { getListAllVanBang },
    } = useDispatch();

    useEffect(() => {
      getListAllChuyenKhoa({ active: true, page: "", size: "" });
      getListAllHocHamHocVi({ active: true, page: "", size: "" });
      getListAllChucVu({ active: true, page: "", size: "" });
      getListAllVanBang({ active: true, page: "", size: "" });
    }, []);

    useEffect(() => {
      //if have id = edit else = create
      if (dataEditDefault?.id) {
        updateForm(dataEditDefault);
      } else {
        form.resetFields();
        if (refAutoFocus.current) {
          setTimeout(() => {
            refAutoFocus.current.focus();
          }, 50);
        }
      }
    }, [dataEditDefault]);

    const dsCoSoChiNhanh = useMemo(() => {
      return listAllChiNhanh.map((item) => ({
        ...item,
        ten: `${item.ma} - ${item.ten} - ${item.donViKcb.ten}`,
      }));
    }, [listAllChiNhanh]);

    const updateForm = (_data) => {
      const data = cloneDeep(_data);
      data.ngaySinh = data.ngaySinh && moment(data.ngaySinh);
      data.tuThoiGianKhoa = data.tuThoiGianKhoa && moment(data.tuThoiGianKhoa);
      data.denThoiGianKhoa =
        data.denThoiGianKhoa && moment(data.denThoiGianKhoa);
      data.dsChuyenKhoaId = data.dsChuyenKhoaId || [];
      form.setFieldsValue(data);
      setLogo(data.anhDaiDien ?? null);
      setAnhKy(data.anhKy ?? null);
    };

    const onUpdateData = (item, type) => {
      if (type === "anhKy") {
        setAnhKy(item);
      } else {
        setLogo(item);
      }
      form.setFieldsValue({ [type]: item });
    };

    const handleRedirect = (type) => (e) => {
      const ma = form.getFieldValue("ma");
      openInNewTab(
        `/quan-tri/danh-muc-tai-khoan?type=${type}&maNhanVien=${ma}`
      );
    };

    const handleAdded = (e) => {
      if (e?.preventDefault) e.preventDefault();
      form.validateFields().then(async (values) => {
        let params = {};
        if (editStatus) {
          values = { ...values, id: dataEditDefault.id };
        }
        try {
          showLoading();
          const res = await createOrEdit(values);
          if (!editStatus) {
            if (res?.id && dataBanSao?.id) {
              await postTTKhoaPhong({
                id: res.id,
                payload: dataBanSao.dsKhoa,
              });
              updateData({ dataBanSao: {} });
            }
            params.page = PAGE_DEFAULT;
            form.resetFields();
          }
          getListNhanVien(params);
          hideLoading();
        } catch {
          hideLoading();
        }
      });
    };

    const handleCancel = () => {
      if (editStatus) {
        updateForm(dataEditDefault);
      } else {
        form.resetFields();
      }
    };

    const getTabIndex = (index) => {
      return 1000 + index;
    };

    const onCreateTextSign = async () => {
      try {
        try {
          let imageSignByPad = await fileUtils.getChuKyTay();
          imageSignByPad = imageSignByPad.includes("data:image/png;base64")
            ? imageSignByPad
            : `data:image/png;base64,${imageSignByPad}`;
          const file = fileUtils.base64ToFile(
            imageSignByPad,
            stringUtils.guid() + ".png"
          );
          const s = await fileProvider.uploadImage({
            file,
            type: "anhKyNhanVien",
          });
          onUpdateData(s?.data, "anhKy");
        } catch (error) {
          refModalSign.current.show({}, async (base64) => {
            let file = await resizeImage(base64, 320, 160);
            file = fileUtils.base64ToFile(file, stringUtils.guid() + ".png");
            const s = await fileProvider.uploadImage({
              file,
              type: "anhKyNhanVien",
            });
            onUpdateData(s?.data, "anhKy");
          });
        }
      } catch (error) {
        debugger;
      }
    };

    return (
      <Main>
        <CreatedWrapper
          onCancel={handleCancel}
          cancelText={t("common.huy")}
          onOk={handleAdded}
          okText={t("common.luuF4")}
          roleSave={[ROLES["QUAN_LY_TAI_KHOAN"].NHAN_VIEN_THEM]}
          roleEdit={[ROLES["QUAN_LY_TAI_KHOAN"].NHAN_VIEN_SUA]}
          editStatus={editStatus}
          layerId={layerId}
          border={false}
        >
          <fieldset
            disabled={
              editStatus
                ? !checkRole([ROLES["QUAN_LY_TAI_KHOAN"].NHAN_VIEN_SUA])
                : !checkRole([ROLES["QUAN_LY_TAI_KHOAN"].NHAN_VIEN_THEM])
            }
          >
            <Form
              form={form}
              layout="vertical"
              className="form-custom responsive-col"
            >
              <Row style={{ width: "100%" }}>
                <Col span={12}>
                  <Form.Item
                    label={t("danhMuc.hoTen")}
                    name="ten"
                    style={{ width: "100%" }}
                    rules={[
                      {
                        required: true,
                        message: t("danhMuc.vuiLongNhapHoTen"),
                      },
                      {
                        max: 255,
                        message: t("danhMuc.vuiLongNhapHoTenKhongQua255KyTu"),
                      },
                    ]}
                  >
                    <Input
                      ref={refAutoFocus}
                      className="input-option"
                      placeholder={t("danhMuc.vuiLongNhapHoTen")}
                      maxLength={255}
                      tabIndex={getTabIndex(1)}
                    />
                  </Form.Item>

                  <Form.Item
                    label={t("common.ngaySinh")}
                    name="ngaySinh"
                    style={{ width: "100%" }}
                  >
                    <DateTimePicker
                      format={BIRTHDAY_FORMAT}
                      showTime={false}
                      placeholder={t("common.ngaySinh")}
                      tabIndex={getTabIndex(3)}
                    />
                  </Form.Item>
                  <Form.Item
                    label={t("danhMuc.soBHXH")}
                    name="soBaoHiemXaHoi"
                    style={{ width: "100%" }}
                  >
                    <Input
                      className="input-option"
                      placeholder={t("danhMuc.soBHXH")}
                      tabIndex={getTabIndex(5)}
                    />
                  </Form.Item>
                  <Form.Item
                    label={t("common.gioiTinh")}
                    name="gioiTinh"
                    style={{ width: "100%" }}
                  >
                    <Select
                      className="input-option"
                      placeholder={t("common.chonGioiTinh")}
                      data={listGioiTinh}
                      tabIndex={getTabIndex(7)}
                    />
                  </Form.Item>
                  <Form.Item
                    label="Email"
                    name="email"
                    style={{ width: "100%" }}
                  >
                    <Input
                      className="input-option"
                      placeholder="Email"
                      tabIndex={getTabIndex(9)}
                    />
                  </Form.Item>
                  <Form.Item
                    label={t("common.soDienThoai")}
                    name="soDienThoai"
                    style={{ width: "100%" }}
                  >
                    <Input
                      className="input-option"
                      placeholder={t("common.nhapSoDienThoai")}
                      tabIndex={getTabIndex(11)}
                    />
                  </Form.Item>
                  <Form.Item
                    label={t("common.cmndCanCuoc")}
                    name="soCanCuoc"
                    style={{ width: "100%" }}
                  >
                    <Input
                      className="input-option"
                      placeholder={t("common.cmndCanCuoc")}
                      tabIndex={getTabIndex(13)}
                    />
                  </Form.Item>
                  <Form.Item
                    label={t("quanTriHeThong.ctsTenTaiKhoanKy")}
                    name="taiKhoanKy"
                    style={{ width: "100%" }}
                  >
                    <Input
                      className="input-option"
                      placeholder={t("quanTriHeThong.ctsTenTaiKhoanKy")}
                      tabIndex={getTabIndex(15)}
                    />
                  </Form.Item>
                  <Form.Item
                    label={t("danhMuc.taiKhoanHDDT")}
                    name="taiKhoanHddt"
                    style={{ width: "100%" }}
                  >
                    <Input
                      className="input-option"
                      placeholder={t("danhMuc.taiKhoanHDDT")}
                      tabIndex={getTabIndex(17)}
                    />
                  </Form.Item>
                  <Form.Item
                    label={t("danhMuc.matKhauHDDT")}
                    name="matKhauHddt"
                    style={{ width: "100%" }}
                  >
                    <Input.Password
                      autoComplete="new-password"
                      className="input-option"
                      placeholder={t("danhMuc.matKhauHDDT")}
                      tabIndex={getTabIndex(17)}
                      iconRender={(visible) =>
                        visible ? (
                          <Tooltip
                            title={t("danhMuc.hienThiThongTin")}
                            placement="bottom"
                          >
                            <EyeTwoTone />
                          </Tooltip>
                        ) : (
                          <Tooltip
                            title={t("danhMuc.anThongTin")}
                            placement="bottom"
                          >
                            <EyeInvisibleOutlined />
                          </Tooltip>
                        )
                      }
                    />
                  </Form.Item>
                  <Form.Item
                    label={t("danhMuc.taiKhoanLienThongDTDT")}
                    name="taiKhoanDonThuoc"
                    style={{ width: "100%" }}
                  >
                    <Input
                      className="input-option"
                      placeholder={t("danhMuc.taiKhoanLienThongDTDT")}
                      tabIndex={getTabIndex(19)}
                    />
                  </Form.Item>
                  <Form.Item
                    label={t("danhMuc.matKhauLienThongDTDT")}
                    name="matKhauDonThuoc"
                    style={{ width: "100%" }}
                  >
                    <Input.Password
                      autoComplete="new-password"
                      className="input-option"
                      placeholder={t("danhMuc.matKhauLienThongDTDT")}
                      tabIndex={getTabIndex(21)}
                      iconRender={(visible) =>
                        visible ? (
                          <Tooltip
                            title={t("danhMuc.hienThiThongTin")}
                            placement="bottom"
                          >
                            <EyeTwoTone />
                          </Tooltip>
                        ) : (
                          <Tooltip
                            title={t("danhMuc.anThongTin")}
                            placement="bottom"
                          >
                            <EyeInvisibleOutlined />
                          </Tooltip>
                        )
                      }
                    />
                  </Form.Item>
                  <Form.Item
                    label={t("danhMuc.coSoChiNhanh")}
                    name="dsCoSoKcbId"
                    style={{ width: "100%" }}
                    rules={[
                      {
                        required: true,
                        message: t("danhMuc.vuiLongChonCoSoChiNhanh"),
                      },
                    ]}
                  >
                    <Select
                      mode="multiple"
                      tabIndex={getTabIndex(23)}
                      placeholder={t("danhMuc.chonCoSoChiNhanh")}
                      data={dsCoSoChiNhanh}
                    />
                  </Form.Item>
                  <div style={{ display: "flex" }}>
                    <Form.Item
                      label={t("common.anhDaiDien")}
                      name="anhDaiDien"
                      style={{ width: "100%" }}
                    >
                      <ListImage
                        uploadImage={(e) => onUpdateData(e, "anhDaiDien")}
                        files={logo}
                        provider="nhanVien"
                        showUploadList={false}
                        tabIndex={getTabIndex(23)}
                      />
                    </Form.Item>
                    <Form.Item
                      label={t("danhMuc.anhChuKy")}
                      name="anhKy"
                      style={{ width: "100%" }}
                    >
                      <ListImage
                        uploadImage={(e) => onUpdateData(e, "anhKy")}
                        files={anhKy}
                        provider="anhKyNhanVien"
                        showUploadList={false}
                        tabIndex={getTabIndex(25)}
                      />
                      <Button onClick={onCreateTextSign}>
                        {t("danhMuc.chuKyTay")}
                      </Button>
                    </Form.Item>
                  </div>
                  <div style={{ display: "flex" }}>
                    <Form.Item
                      name="online"
                      valuePropName="checked"
                      style={{ width: "100%", marginTop: 16 }}
                    >
                      <Checkbox tabIndex={getTabIndex(27)}>
                        {t("danhMuc.datKhamOnline")}
                      </Checkbox>
                    </Form.Item>
                    <Form.Item
                      name="active"
                      valuePropName="checked"
                      style={{ width: "100%", marginTop: 16 }}
                    >
                      <Checkbox tabIndex={getTabIndex(29)}>
                        {t("danhMuc.coHieuLuc")}
                      </Checkbox>
                    </Form.Item>
                  </div>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label={t("danhMuc.maNhanVien")}
                    name="ma"
                    style={{ width: "100%" }}
                    rules={[
                      {
                        required: true,
                        message: t("danhMuc.vuiLongNhapMaNhanVien"),
                      },
                    ]}
                  >
                    <Input
                      className="input-option"
                      placeholder={t("danhMuc.maNhanVien")}
                      tabIndex={getTabIndex(2)}
                    />
                  </Form.Item>
                  <Form.Item
                    label={
                      editStatus && dataEditDefault?.active ? (
                        <div
                          className="pointer flex flex-center gap-8"
                          onClick={handleRedirect(taiKhoan2 ? "view" : "add")}
                        >
                          {t("danhMuc.tenTaiKhoan")}
                          <Tooltip
                            title={
                              taiKhoan2
                                ? t("common.xemChiTiet")
                                : t("common.themMoi")
                            }
                          >
                            {taiKhoan2 ? <SVG.IcEye /> : <SVG.IcAdd />}
                          </Tooltip>
                        </div>
                      ) : (
                        t("danhMuc.tenTaiKhoan")
                      )
                    }
                    name="taiKhoan2"
                    style={{ width: "100%" }}
                  >
                    <Input
                      className="input-option"
                      placeholder={t("danhMuc.nhapTenTaiKhoan")}
                      disabled
                      tabIndex={getTabIndex(4)}
                    />
                  </Form.Item>
                  <Form.Item
                    label={
                      <div
                        className="pointer"
                        onClick={() =>
                          openInNewTab("/danh-muc/van-bang-chuyen-mon")
                        }
                      >
                        {t("danhMuc.bangChuyenMon")}
                      </div>
                    }
                    name="vanBangId"
                    style={{ width: "100%" }}
                  >
                    <Select
                      placeholder={t("danhMuc.bangChuyenMon")}
                      data={listAllVanBang}
                      tabIndex={getTabIndex(6)}
                    />
                  </Form.Item>

                  <Form.Item
                    label={
                      <div
                        className="pointer"
                        onClick={() => openInNewTab("/danh-muc/chuyen-khoa")}
                      >
                        {t("danhMuc.chuyenKhoa")}
                      </div>
                    }
                    name="dsChuyenKhoaId"
                    style={{ width: "100%" }}
                  >
                    <Select
                      data={listAllChuyenKhoa}
                      mode="multiple"
                      placeholder={t("danhMuc.chuyenKhoa")}
                      tabIndex={getTabIndex(8)}
                    />
                  </Form.Item>

                  <Form.Item
                    label={
                      <div
                        className="pointer"
                        onClick={() => openInNewTab("/danh-muc/hoc-ham-hoc-vi")}
                      >
                        {t("danhMuc.hocViHamVi")}
                      </div>
                    }
                    name="hocHamHocViId"
                    style={{ width: "100%" }}
                  >
                    <Select
                      data={listAllHocHamHocVi}
                      placeholder={t("danhMuc.hocViHamVi")}
                      tabIndex={getTabIndex(10)}
                    />
                  </Form.Item>
                  <Form.Item
                    label={t("danhMuc.chungChi")}
                    name="chungChi"
                    style={{ width: "100%" }}
                  >
                    <Input
                      className="input-option"
                      placeholder={t("danhMuc.chungChi")}
                      tabIndex={getTabIndex(12)}
                    />
                  </Form.Item>
                  <Form.Item
                    label={
                      <div
                        className="pointer"
                        onClick={() => openInNewTab("/danh-muc/chuc-vu")}
                      >
                        {t("danhMuc.chucVu")}
                      </div>
                    }
                    name="chucVuId"
                    style={{ width: "100%" }}
                  >
                    <Select
                      data={listAllChucVu}
                      placeholder={t("danhMuc.chucVu")}
                      tabIndex={getTabIndex(14)}
                    />
                  </Form.Item>
                  {dataDOI_MAT_KHAU_KY_SO?.eval() &&
                    <Form.Item
                      label={t("quanTriHeThong.pinMkKy")}
                      name="matKhauKy"
                      style={{ width: "100%" }}
                    >
                      <Input.Password
                        autoComplete="new-password"
                        className="input-option"
                        placeholder={t("quanTriHeThong.pinMkKy")}
                        tabIndex={getTabIndex(16)}
                        iconRender={(visible) =>
                          visible ? (
                            <Tooltip
                              title={t("danhMuc.hienThiThongTin")}
                              placement="bottom"
                            >
                              <EyeTwoTone />
                            </Tooltip>
                          ) : (
                            <Tooltip
                              title={t("danhMuc.anThongTin")}
                              placement="bottom"
                            >
                              <EyeInvisibleOutlined />
                            </Tooltip>
                          )
                        }
                      />
                    </Form.Item>
                  }
                  <Form.Item
                    label={t("quanTriHeThong.otpKy")}
                    name="otpKy"
                    style={{ width: "100%" }}
                  >
                    <Input.Password
                      autoComplete="off"
                      className="input-option"
                      placeholder={t("quanTriHeThong.otpKy")}
                      tabIndex={getTabIndex(18)}
                      iconRender={(visible) =>
                        visible ? (
                          <Tooltip
                            title={t("danhMuc.hienThiThongTin")}
                            placement="bottom"
                          >
                            <EyeTwoTone />
                          </Tooltip>
                        ) : (
                          <Tooltip
                            title={t("danhMuc.anThongTin")}
                            placement="bottom"
                          >
                            <EyeInvisibleOutlined />
                          </Tooltip>
                        )
                      }
                    />
                  </Form.Item>
                  <Form.Item
                    label={t("danhMuc.danhHieu")}
                    name="danhHieu"
                    style={{ width: "100%" }}
                  >
                    <Input
                      className="input-option"
                      placeholder={t("danhMuc.danhHieu")}
                      tabIndex={getTabIndex(20)}
                    />
                  </Form.Item>
                  <Form.Item
                    label={t("danhMuc.tenNganHang")}
                    name="tenNganHang"
                    style={{ width: "100%" }}
                  >
                    <Input
                      className="input-option"
                      placeholder={t("danhMuc.nhapTenNganHang")}
                      tabIndex={getTabIndex(22)}
                    />
                  </Form.Item>
                  <Form.Item
                    label={t("danhMuc.soTaiKhoan")}
                    name="soTaiKhoan"
                    style={{ width: "100%" }}
                  >
                    <Input
                      className="input-option"
                      placeholder={t("danhMuc.nhapSoTaiKhoan")}
                      tabIndex={getTabIndex(24)}
                    />
                  </Form.Item>
                  <Form.Item
                    label={t("quanTriHeThong.khoaTuNgay")}
                    name="tuThoiGianKhoa"
                    style={{ width: "100%" }}
                    {...(denThoiGianKhoa &&
                      tuThoiGianKhoa && {
                      rules: [
                        {
                          validator: (rules, value) => {
                            if (value > denThoiGianKhoa) {
                              return Promise.reject(
                                new Error(
                                  t("quanTriHeThong.khoaTuNgayPhaiNhoHon")
                                )
                              );
                            } else {
                              form.setFields([
                                {
                                  name: "denThoiGianKhoa",
                                  errors: [],
                                },
                              ]);
                            }
                            return Promise.resolve();
                          },
                        },
                      ],
                    })}
                  >
                    <DateTimePicker
                      showTime={false}
                      tabIndex={getTabIndex(26)}
                      formatDefaultTime={{
                        hour: "00",
                        minute: "00",
                        second: "00",
                      }}
                      placeholder={t("quanTriHeThong.khoaTuNgay")}
                      {...(denThoiGianKhoa && {
                        disabledDate: (date) => date > denThoiGianKhoa,
                      })}
                    />
                  </Form.Item>
                  <Form.Item
                    label={t("quanTriHeThong.khoaDenNgay")}
                    name="denThoiGianKhoa"
                    style={{ width: "100%" }}
                    {...(tuThoiGianKhoa &&
                      denThoiGianKhoa && {
                      rules: [
                        {
                          validator: (rules, value) => {
                            if (value < tuThoiGianKhoa) {
                              return Promise.reject(
                                new Error(
                                  t("quanTriHeThong.khoaDenNgayPhaiLonHon")
                                )
                              );
                            } else {
                              form.setFields([
                                {
                                  name: "tuThoiGianKhoa",
                                  errors: [],
                                },
                              ]);
                            }
                            return Promise.resolve();
                          },
                        },
                      ],
                    })}
                  >
                    <DateTimePicker
                      showTime={false}
                      formatDefaultTime={{
                        hour: "23",
                        minute: "59",
                        second: "59",
                      }}
                      tabIndex={getTabIndex(28)}
                      placeholder={t("quanTriHeThong.khoaDenNgay")}
                      {...(tuThoiGianKhoa && {
                        disabledDate: (date) => date < tuThoiGianKhoa,
                      })}
                    />
                  </Form.Item>
                  <Form.Item
                    label={t("danhMuc.ghiChu")}
                    name="ghiChu"
                    style={{ width: "100%" }}
                  >
                    <TextArea
                      style={{ minHeight: 100 }}
                      className="input-option"
                      placeholder={t("danhMuc.ghiChu")}
                      tabIndex={getTabIndex(30)}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </fieldset>
        </CreatedWrapper>
        <ModalPatientSign ref={refModalSign}></ModalPatientSign>
      </Main>
    );
  }
);

export default ThongTinChiTiet;
