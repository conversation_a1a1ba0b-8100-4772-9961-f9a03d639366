import React, { useState, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { <PERSON><PERSON>, AuthWrapper, DatePicker, Popover, Select } from "components";
import moment from "moment";
import { message } from "antd";
import classNames from "classnames";
import { useEnum, useLoading, useThietLap } from "hooks";
import {
  setQueryStringValue,
  setQueryStringValues,
} from "hooks/useQueryString/queryString";
import fileUtils from "utils/file-utils";
import { isArray, transformQueryString } from "utils/index";
import { ENUM, HOTKEY, ROLES, THIET_LAP_CHUNG } from "constants/index";
import { TIME_FORMAT } from "../configs";
import { SVG } from "assets";
import { Main, GlobalStyle } from "./styled";

const PhieuThuHeader = ({
  layerId,
  title,
  HeaderThu<PERSON>gan,
  parentState,
  setParentState = () => {},
  checkIsOnly = () => false,
}) => {
  const [listTrangThaDuyetBH] = useEnum(ENUM.TRANG_THAI_DUYET_BAO_HIEM);
  const { t } = useTranslation();
  const refDatePicker = useRef(null);
  const [fileUrl, setFileUrl] = useState(null);
  const [active, setActive] = useState({
    ngoaiTru: false,
    noiTru: false,
  });
  const { showLoading, hideLoading } = useLoading();
  const {
    chuaThanhToan,
    daThanhToan,
    tongSo,
    choDuyetQrNgoaiTru,
    choDuyetQrNoiTru,
    dataSearch,
  } = useSelector((state) => state.danhSachPhieuThu);

  const [dataHIEN_THI_THONG_KE_CHO_DUYET_PHIEU_THU_QR] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_THONG_KE_CHO_DUYET_PHIEU_THU_QR
  );

  const {
    danhSachPhieuThu: { onChangeInputSearch, exportExcel },
    phimTat: { onRegisterHotkey },
  } = useDispatch();

  const [state, _setState] = useState(() => {
    const {
      tuThoiGianThanhToan,
      denThoiGianThanhToan,
      tuThoiGianRaVien,
      denThoiGianRaVien,
      tuThoiGianVaoVien,
      denThoiGianVaoVien,
      dsTrangThaiDuyetBaoHiem,
    } = transformQueryString({
      tuThoiGianThanhToan: {
        format: (value) => (value ? moment(+value) : null),
        defaultValue: null,
      },
      denThoiGianThanhToan: {
        format: (value) => (value ? moment(+value) : null),
        defaultValue: null,
      },
      tuThoiGianRaVien: {
        format: (value) => (value ? moment(+value) : null),
        defaultValue: null,
      },
      denThoiGianRaVien: {
        format: (value) => (value ? moment(+value) : null),
        defaultValue: null,
      },
      tuThoiGianVaoVien: {
        type: "dateOptions",
        defaultValue: moment().startOf("day"),
      },
      denThoiGianVaoVien: {
        type: "dateOptions",
        defaultValue: moment().endOf("day"),
      },
      dsTrangThaiDuyetBaoHiem: {
        format: (value) => (value ? value.split(",").map(Number) : []),
        defaultValue: [],
      },
    });

    return {
      tuThoiGianThanhToan,
      denThoiGianThanhToan,
      tuThoiGianRaVien,
      denThoiGianRaVien,
      tuThoiGianVaoVien,
      denThoiGianVaoVien,
      dsTrangThaiDuyetBaoHiem,
    };
  });

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  //effect
  useEffect(() => {
    onRegisterHotkey({
      layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.TAB, //Tab
          onEvent: () => {
            refDatePicker.current && refDatePicker.current.focus();
          },
        },
      ],
    });
  }, []);

  useEffect(() => {
    if (fileUrl) {
      fileUtils
        .getFromUrl({ url: fileUtils.absoluteFileUrl(fileUrl) })
        .then((s) => {
          const blob = new Blob([new Uint8Array(s)], {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          });
          fileUtils.downloadBlob(blob, `${fileUrl}`); //or any other extension
        });
    }
  }, [fileUrl]);

  const handleChangeDate = (key) => (date) => {
    setQueryStringValue(key, date);
    setState({ [key]: date });
    onChangeInputSearch({
      [key]: date ? moment(date).format("YYYY-MM-DDTHH:mm:ss") : "",
      getThongKe: true,
    });
  };

  const handleChangeTrangThaiDuyetBH = (key) => (value) => {
    setQueryStringValue(key, value);
    setState({ [key]: value });
    onChangeInputSearch({
      [key]: value,
      getThongKe: true,
    });
  };

  const handleStartDate = (time) => {
    return moment(time) > moment();
  };

  const handleEndDate = (key) => (time) => {
    if (key === "denThoiGianThanhToan") {
      return moment(time) < moment(state.tuThoiGianThanhToan);
    } else if (key === "denThoiGianRaVien") {
      return moment(time) < moment(state.tuThoiGianRaVien);
    }
    {
      return moment(time) < moment(state.tuThoiGianVaoVien);
    }
  };

  function onClickXuatDS() {
    showLoading();

    let { dsTrangThaiTktt, dsNguoiThucHienId, ...rest } = dataSearch || {};

    let body = {
      ...rest,
      dsTrangThaiTktt:
        !isArray(dsTrangThaiTktt, true) && dsTrangThaiTktt
          ? [dsTrangThaiTktt]
          : dsTrangThaiTktt ?? null,
      dsNguoiThucHienId:
        !isArray(dsNguoiThucHienId, true) && dsNguoiThucHienId
          ? [dsNguoiThucHienId]
          : dsNguoiThucHienId ?? null,
    };

    exportExcel(body)
      .then((data) => {
        if (data.file.doc) {
          setFileUrl(data.file?.doc);
        }
      })
      .catch((e) => {
        message.error(e?.message);
      })
      .finally(() => {
        hideLoading();
      });
  }

  function onClickMHPhu() {
    window.open("/thu-ngan/man-hinh-phu", "_blank");
  }

  const onClickChoDuyetQr = (isNoiTru) => (e) => {
    let key = isNoiTru ? "noiTru" : "ngoaiTru";
    setActive({
      ...active,
      [key]: !active[key],
    });
    let params = {};
    if (!active[key]) {
      params = {
        dsTrangThaiThanhToan: 50,
        thanhToan: -1,
        dsDoiTuongKcb: [...(isNoiTru ? [2, 3, 4, 6, 9] : [1, 5, 7, 8, 10])],
      };
    } else {
      params = {
        dsTrangThaiThanhToan: parentState?.dsTrangThaiThanhToan,
        thanhToan: parentState?.thanhToan,
        dsDoiTuongKcb: parentState?.dsDoiTuongKcb,
      };
    }
    setQueryStringValues(params);
    setState({ ...params });
    setParentState(params);
    onChangeInputSearch({
      ...params,
      isOnly: checkIsOnly(dataSearch),
      getThongKe: true,
    });
  };

  const group = (
    <>
      <div className="filter">
        <div className="title">{t("thuNgan.ngayThanhToan")}</div>
        <div>
          <DatePicker
            ref={refDatePicker}
            format={TIME_FORMAT}
            disabledDate={handleStartDate}
            placeholder={t("common.tuNgay")}
            value={state.tuThoiGianThanhToan}
            showTime={{ defaultValue: moment().startOf("day") }}
            onChange={handleChangeDate("tuThoiGianThanhToan")}
          />
          <span className="spread">-</span>
          <DatePicker
            format={TIME_FORMAT}
            placeholder={t("common.denNgay")}
            disabledDate={handleEndDate("denThoiGianThanhToan")}
            showTime={{ defaultValue: moment().endOf("day") }}
            value={state.denThoiGianThanhToan}
            onChange={handleChangeDate("denThoiGianThanhToan")}
          />{" "}
        </div>
      </div>
      <div className="filter" style={{ paddingTop: "20px" }}>
        <div className="title">{t("thuNgan.ngayRaVien")}</div>
        <div>
          <DatePicker
            ref={refDatePicker}
            format={TIME_FORMAT}
            disabledDate={handleStartDate}
            placeholder={t("common.tuNgay")}
            value={state.tuThoiGianRaVien}
            showTime={{ defaultValue: moment().startOf("day") }}
            onChange={handleChangeDate("tuThoiGianRaVien")}
          />
          <span className="spread">-</span>
          <DatePicker
            format={TIME_FORMAT}
            placeholder={t("common.denNgay")}
            disabledDate={handleEndDate("denThoiGianRaVien")}
            showTime={{ defaultValue: moment().endOf("day") }}
            value={state.denThoiGianRaVien}
            onChange={handleChangeDate("denThoiGianRaVien")}
          />{" "}
        </div>
      </div>
      <div className="filter" style={{ paddingTop: "20px" }}>
        <div className="title"> {t("thuNgan.ngayDangKy")} </div>
        <div>
          <DatePicker
            ref={refDatePicker}
            format={TIME_FORMAT}
            disabledDate={handleStartDate}
            placeholder={t("common.tuNgay")}
            value={state.tuThoiGianVaoVien}
            showTime={{ defaultValue: moment().startOf("day") }}
            onChange={handleChangeDate("tuThoiGianVaoVien")}
          />
          <span className="spread">-</span>
          <DatePicker
            format={TIME_FORMAT}
            placeholder={t("common.denNgay")}
            disabledDate={handleEndDate("denThoiGianVaoVien")}
            showTime={{ defaultValue: moment().endOf("day") }}
            value={state.denThoiGianVaoVien}
            onChange={handleChangeDate("denThoiGianVaoVien")}
          />
        </div>
      </div>
      <div className="filter" style={{ paddingTop: "20px" }}>
        <div className="title"> {t("thuNgan.trangThaiDuyetBH")} </div>
        <div>
          <Select
            mode={"multiple"}
            data={listTrangThaDuyetBH}
            value={state.dsTrangThaiDuyetBaoHiem}
            onChange={handleChangeTrangThaiDuyetBH("dsTrangThaiDuyetBaoHiem")}
            placeholder={t("thuNgan.chonTrangThaiDuyetBH")}
          />
        </div>
      </div>
    </>
  );

  return (
    <Main>
      {HeaderThuNgan && (
        <HeaderThuNgan
          locPhieuThu={
            <Popover
              trigger="click"
              content={group}
              placement="bottomLeft"
              overlayClassName="popover-thungan"
            >
              <Button
                className="filter"
                leftIcon={<SVG.IcFilter />}
                iconHeight={15}
              >
                <span> {t("thuNgan.locPhieuThu")}</span>
              </Button>
            </Popover>
          }
          trangThaiPhieuThu={
            <>
              <span>
                {t("thuNgan.tongPhieuThu")}:{" "}
                <span className="bold">{tongSo || 0}</span>
              </span>
              <span>
                {t("thuNgan.chuaThanhToan")}:{" "}
                <span className="bold">{chuaThanhToan || 0}</span>
              </span>
              <span>
                {t("thuNgan.daThanhToan")}:{" "}
                <span className="bold">{daThanhToan || 0}</span>
              </span>
              {dataHIEN_THI_THONG_KE_CHO_DUYET_PHIEU_THU_QR?.eval() && (
                <>
                  <span
                    onClick={onClickChoDuyetQr(false)}
                    className={classNames("cursor-pointer", {
                      active: active.ngoaiTru,
                    })}
                  >
                    {t("thuNgan.choDuyetQrNgoaiTru")}:{" "}
                    <span className="bold">{choDuyetQrNgoaiTru || 0}</span>
                  </span>
                  <span
                    onClick={onClickChoDuyetQr(true)}
                    className={classNames("cursor-pointer", {
                      active: active.noiTru,
                    })}
                  >
                    {t("thuNgan.choDuyetQrNoiTru")}:{" "}
                    <span className="bold">{choDuyetQrNoiTru || 0}</span>
                  </span>
                </>
              )}
            </>
          }
        />
      )}
      <GlobalStyle />

      <div className="header__container">
        <div className="left">
          <div className="title-category">{title}</div>
        </div>
        <div className="right">
          <AuthWrapper accessRoles={[ROLES["THU_NGAN"].DANH_SACH_PHIEU_THU]}>
            <Button
              rightIcon={<SVG.IcList />}
              iconHeight={15}
              type="primary"
              onClick={onClickXuatDS}
            >
              {t("thuNgan.xuatDS")}
            </Button>
          </AuthWrapper>

          <AuthWrapper accessRoles={[ROLES["THU_NGAN"].BAT_MAN_HINH_PHU]}>
            <Button iconHeight={15} type="primary" onClick={onClickMHPhu}>
              {t("thuNgan.batMHPhu")}
            </Button>
          </AuthWrapper>
        </div>
      </div>
    </Main>
  );
};

export default PhieuThuHeader;
