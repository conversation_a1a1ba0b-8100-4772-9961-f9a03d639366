import styled from "styled-components";
export const Main = styled.div`
  padding: 8px 16px 0;

  .title-search {
    flex: 0 0 670px;
  }

  & div.ant-row-middle:first-child {
    align-items: start;
  }
  background: linear-gradient(
      0deg,
      rgba(255, 255, 255, 0.95),
      rgba(255, 255, 255, 0.95)
    ),
    #0762f7;
  box-shadow: 0px 0px 15px rgba(9, 30, 66, 0.07);

  .input-search {
    background: #ffffff;
    border: 2px solid #049254;
    box-sizing: border-box;
    border-radius: 50px;
    display: flex;
    flex: 1;
    align-items: center;
    padding: 8px 14px 8px 12px;
    &:focus-within {
      box-shadow: 0px 0px 0px 3px #01955447;
    }
    input {
      padding: 0 1em 0 0 !important;
      border: none;
      /* border-radius: 50px; */
      font-weight: 600;
      font-size: 16px;
      line-height: 20px;
      padding-right: 1em;

      &:hover {
        border: none !important;
        box-shadow: none !important;
      }
      &:focus {
        border: none !important;
        box-shadow: none !important;
      }
      &::placeholder {
        color: #7a869a;
      }
    }
    img {
      height: 18px;
    }
  }
  .next-patient {
    display: flex;
    justify-content: end;
    align-items: center;
    .chon-ca-lam-viec {
      margin: 0;
      padding: 0;
    }
    .chon-quay-thu-ngan {
      margin: 0;
    }

    .boLoc {
      padding: 0 2px;
      .ant-select {
        width: 160px;
        .ant-select-selector {
          padding-left: 20px;
          .ant-select-selection-search {
            padding-left: 10px;
          }
        }
      }
      > svg {
        position: absolute;
        z-index: 1;
        margin-top: 5px;
      }

      input {
        width: 100px;
      }
    }
    .boLoc2 {
      padding: 0 2px;
      .ant-select {
        width: 200px;
      }
    }
    & .item {
      box-shadow: 0px 0px 15px rgb(9 30 66 / 7%);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 5px;
      margin-left: 10px;
      cursor: pointer;
      svg {
        height: 44px;
        width: 44px;
        rect {
          fill: #0762f7 !important;
        }
      }
      rect:hover {
        fill: #032254 !important;
      }
    }
  }
  .next-patient-bottom {
    display: flex;
    justify-content: end;
    padding-bottom: 5px;
    align-items: center;
    gap: 5px;
    width: 100%;
    > span {
      display: inline-block;
      padding: 10px 6px;
      border-radius: 8px;
      font-style: normal;
      font-weight: 400;
      font-size: 13px;
      color: #172b4d;
      &:nth-child(1) {
        background: #c1f3f7;
      }
      &:nth-child(2) {
        background: #c1d8fd;
      }
      &:nth-child(3) {
        background: #d9c0f2;
      }
      &:nth-child(4) {
        background: #fecece;
        border: 1px solid #fecece;
        transition: all 0.2s ease;
        &.active {
          background: #f89c9c;
          border-color: #e06767;
        }
      }

      &:nth-child(5) {
        background: #fef9c3;
        border: 1px solid #fef9c3;
        transition: all 0.2s ease;
        &.active {
          background: #f4e86e;
          border-color: #d6c94a;
        }
      }
      .bold {
        font-weight: 900;
      }
    }
    .btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-top: 2px;

      button {
        height: 36px;
      }
      span {
        font-size: 12px;
      }
    }

    .icon-action {
      margin-left: 0;
    }
  }
  & .button-gopage {
    padding-left: 5px;
    .danh-sach {
      svg {
        path {
          fill: #0762f7 !important;
        }
      }
    }
  }
  .btn-dong-quay {
    margin: 0 !important;
  }

  @media (width>=1920px) {
    .title-search {
      flex: 0 0 40%;
    }
  }
`;
export const TitleSearch = styled.div`
  font-weight: 600;
  font-size: 14px;
  line-height: 16px;
  color: #172b4d;
  padding-bottom: 4px;
`;
export const WrapperPopover = styled.div`
  margin: -12px -16px;
  .item-popover {
    padding: 5px 10px;
    cursor: pointer;
    :hover {
      background-color: #ccc;
    }
  }
`;
