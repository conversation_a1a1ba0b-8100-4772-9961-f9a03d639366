import React, { useEffect, useMemo, useState } from "react";
import {
  HeaderSearch,
  Checkbox,
  TableWrapper,
  InputTimeout,
  Select,
} from "components";
import { formatDecimal, parseFloatNumber } from "utils";
import { useTranslation } from "react-i18next";
import { InputNumberFormat } from "components/common";
import { TRANG_THAI_PHIEU_THU_THANH_TOAN } from "constants/index";
import nbDichVuProvider from "data-access/nb-dich-vu-provider";
import { groupBy } from "lodash";
import { InputNumberFormatKpis } from "pages/kpis/chiSoKpis/components/InputNumberFormatKpis";

let timer = null;

const LIST_MIEN_GIAM = [
  { id: 10, i18n: "thuNgan.tienMienGiamSoTienCungChiTra" },
  { id: 20, i18n: "thuNgan.tienMienGiamSoTienTuTra" },
];

const DiscountOnService = ({
  updateListServices,
  thongTinPhieuThu,
  onUpdateReceipt,
  ghiChu,
}) => {
  const { t } = useTranslation();

  const [state, _setState] = useState({
    data: [],
    selectedRowKeys: [],
    dataSearch: {},
    listData: [],
    checkAll: false,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useEffect(() => {
    if (thongTinPhieuThu.nbDotDieuTriId && thongTinPhieuThu.id)
      nbDichVuProvider
        .searchAll({
          nbDotDieuTriId: thongTinPhieuThu.nbDotDieuTriId,
          phieuThuId: thongTinPhieuThu.id,
          page: "",
          size: "",
        })
        .then((s) => {
          setState({ data: s?.data });
        });
  }, [thongTinPhieuThu]);

  const onSearchInput = (key) => (e) => {
    const searchValue = e;
    const valueText = searchValue?.trim().toLowerCase().unsignText();
    const dataSearch = { ...state.dataSearch, [key]: valueText };
    setState({ dataSearch: { ...state.dataSearch, [key]: valueText } });
    nbDichVuProvider
      .searchAll({
        nbDotDieuTriId: thongTinPhieuThu.nbDotDieuTriId,
        phieuThuId: thongTinPhieuThu.id,
        page: "",
        size: "",
        ...dataSearch,
      })
      .then((s) => {
        const { phanTramMienGiamDichVu, tienMienGiamDichVu } = state;
        (s.data || []).forEach((item) => {
          item.phanTramMienGiamDichVu = phanTramMienGiamDichVu;
          item.tienMienGiamDichVu =
            item.thanhTien < tienMienGiamDichVu
              ? item.thanhTien
              : tienMienGiamDichVu;
          return item;
        });
        setState({ data: s?.data });
        updateListServices(s?.data);
      });
  };

  const listDVKT = useMemo(() => {
    const _group = groupBy(state.data, "tenNhomDichVuCap1");
    let data = [];

    Object.keys(_group).forEach((key) => {
      data.push({
        id: key,
        key: key,
        isGroup: true,
        tenDichVu: key,
      });

      (_group[key] || []).forEach((element, index) => {
        data.push({ ...element, index: index + 1, keyParent: key });
      });
    });

    return data;
  }, [state.data]);

  const onSelectChange = (record, selected) => {
    let selectedRowKeys = state.selectedRowKeys;
    let listData = [];
    if (record.isGroup) {
      const data = listDVKT.filter((x) => record.key === x.keyParent);
      if (selected) {
        selectedRowKeys = [
          ...state.selectedRowKeys,
          ...data.map((item) => item.key),
          record.key,
        ];
        listData = [...state.listData, ...data];
      } else {
        selectedRowKeys = state.selectedRowKeys.filter(
          (x) => !data.map((item) => item.key).includes(x) && x != record.key
        );
        listData = state.listData.filter(
          (x) => record.key !== x.keyParent && !x.isGroup
        );
      }
    } else {
      if (selected) {
        const data = listDVKT.filter((x) => record.keyParent === x.keyParent);
        const selectedKey = data.filter((x) =>
          [...state.selectedRowKeys, record.key].includes(x.key)
        );
        if (data.length === selectedKey.length) {
          selectedRowKeys = [
            ...state.selectedRowKeys,
            record.key,
            record.keyParent,
          ];
        } else {
          selectedRowKeys = [...state.selectedRowKeys, record.key];
        }
        listData = [...state.listData, record];
      } else {
        selectedRowKeys = state.selectedRowKeys.filter(
          (x) => x != record.key && x !== record.keyParent
        );
        listData = state.listData.filter((x) => x.key !== record.key);
      }
    }

    const { phanTramMienGiamDichVu, tienMienGiamDichVu, loaiMienGiam } = state;
    const formattedData = listData
      .map((item) => {
        if (selectedRowKeys.includes(item.key)) {
          if (loaiMienGiam) {
            let key = "";
            if (loaiMienGiam === 10) key = "phanTramMienGiamDichVuBh";
            else key = "phanTramMienGiamDichVuKhongBh";
            item[key] = 100;
            item.tienMienGiamDichVu = 0;
            item.phanTramMienGiamDichVu = 0;
          } else if (state.mienGiamTheoTienCong) {
            item.tienMienGiamDichVu =
              item.tienNbTuTra - (item.giaGoc || 0) * item.soLuong;
            item.phanTramMienGiamDichVu = 0;
            item.phanTramMienGiamDichVuBh = null;
            item.phanTramMienGiamDichVuKhongBh = null;
          } else {
            let _tienMienGiamDichVu =
              tienMienGiamDichVu ?? item.tienMienGiamDichVu;
            item.phanTramMienGiamDichVu = phanTramMienGiamDichVu;
            item.tienMienGiamDichVu =
              item.thanhTien < _tienMienGiamDichVu
                ? item.thanhTien
                : _tienMienGiamDichVu;
            item.phanTramMienGiamDichVuBh = null;
            item.phanTramMienGiamDichVuKhongBh = null;
          }

          return item;
        }
        return null;
      })
      .filter((item1) => item1);
    setState({ selectedRowKeys, listData });
    updateListServices(formattedData);
  };

  const onSelectAll = (e) => {
    const { phanTramMienGiamDichVu, tienMienGiamDichVu, loaiMienGiam } = state;
    const formattedData = listDVKT
      .filter((x) => !x.isGroup)
      .map((item) => {
        if (e.target.checked && !item.isGroup) {
          if (loaiMienGiam) {
            let key = "";
            if (loaiMienGiam === 10) key = "phanTramMienGiamDichVuBh";
            else key = "phanTramMienGiamDichVuKhongBh";
            item[key] = 100;
            item.tienMienGiamDichVu = 0;
            item.phanTramMienGiamDichVu = 0;
          } else if (state.mienGiamTheoTienCong) {
            item.tienMienGiamDichVu =
              item.tienNbTuTra - (item.giaGoc || 0) * item.soLuong;
            item.phanTramMienGiamDichVu = 0;
            item.phanTramMienGiamDichVuBh = null;
            item.phanTramMienGiamDichVuKhongBh = null;
          } else {
            let _tienMienGiamDichVu =
              tienMienGiamDichVu ?? item.tienMienGiamDichVu;
            item.phanTramMienGiamDichVu = phanTramMienGiamDichVu;
            item.tienMienGiamDichVu =
              item.thanhTien < _tienMienGiamDichVu
                ? item.thanhTien
                : _tienMienGiamDichVu;
            item.phanTramMienGiamDichVuBh = null;
            item.phanTramMienGiamDichVuKhongBh = null;
          }
          return item;
        } else {
          item.phanTramMienGiamDichVu = null;
          item.tienMienGiamDichVu = null;
          item.phanTramMienGiamDichVuBh = null;
          item.phanTramMienGiamDichVuKhongBh = null;
        }
        return null;
      });
    setState({
      selectedRowKeys: e.target?.checked
        ? listDVKT.map((item) => item.key)
        : [],
      checkAll: e.target?.checked,
      listData: [...listDVKT.filter((x) => !x.isGroup)],
    });
    updateListServices(formattedData);
  };

  const onChangeInput = (key) => (e) => {
    let value = e?.target.value && parseFloatNumber(e.target.value);
    const { selectedRowKeys } = state;
    const formattedData = listDVKT
      .filter((x) => !x.isGroup)
      .map((item) => {
        if (selectedRowKeys.includes(item.key)) {
          if (key === "tienMienGiamDichVu" && item.thanhTien < value) {
            item[key] = item.thanhTien;
          } else {
            item[key] = value;
          }
        }
        return item;
      });

    setState({ [key]: value, data: formattedData });
    updateListServices(
      formattedData.filter((x) => selectedRowKeys.includes(x.key))
    );
  };

  const onChangeSelect = (e) => {
    const value = e;
    let selectedRowKeys = [];
    const formattedData = listDVKT
      .filter(
        (x) =>
          !x.isGroup &&
          (value === 10
            ? x.tienNbCungChiTra > 0
            : value === 20
            ? x.tienNbTuTra > 0
            : true)
      )
      .map((item) => {
        let key = "";
        if (value === 10) key = "phanTramMienGiamDichVuBh";
        else if (value === 20) key = "phanTramMienGiamDichVuKhongBh";
        if (key) {
          item[key] = 100;
        } else {
          item.phanTramMienGiamDichVuBh = null;
          item.phanTramMienGiamDichVuKhongBh = null;
        }
        return item;
      });
    if (value) {
      selectedRowKeys = formattedData.map((item) => item.key) || [];
    } else {
      selectedRowKeys = [];
    }
    setState({
      ["loaiMienGiam"]: value,
      selectedRowKeys,
      listData: [...listDVKT.filter((x) => !x.isGroup)],
    });
    updateListServices(
      formattedData.filter((x) => selectedRowKeys.includes(x.key))
    );
  };

  const onChangeCheckbox = (e) => {
    let checked = e.target.checked;
    const { selectedRowKeys } = state;
    const formattedData = listDVKT
      .filter((x) => !x.isGroup)
      .map((item) => {
        if (selectedRowKeys.includes(item.key)) {
          if (checked) {
            item["tienMienGiamDichVu"] =
              item.tienNbTuTra - (item.giaGoc || 0) * item.soLuong;
          }
        }
        return item;
      });

    setState({ ["mienGiamTheoTienCong"]: checked, data: formattedData });
    updateListServices(
      formattedData.filter((x) => selectedRowKeys.includes(x.key))
    );
  };

  const onChangeListData = (record) => (value) => {
    const formattedData = listDVKT
      .filter((x) => !x.isGroup)
      .map((item) => {
        if (record.id === item.id) {
          item.tienMienGiamDichVu =
            item.thanhTien < value ? item.thanhTien : value;
          return item;
        }
        return item;
      });
    setState({ data: formattedData });
    updateListServices(
      formattedData.filter((x) => state.selectedRowKeys.includes(x.key))
    );
  };

  const sharedOnCell = (row, index) => {
    if (row.isGroup) {
      return { colSpan: 0 };
    }
  };
  const columns = [
    {
      title: <HeaderSearch title={t("common.stt")} className="text-center" />,
      width: 40,
      dataIndex: "index",
      key: "index",
      align: "right",
    },
    {
      title: (
        <HeaderSearch
          title={t("common.tenDichVu")}
          search={
            <InputTimeout
              placeholder={t("thuNgan.timTenDichVu")}
              onChange={onSearchInput("tenDichVu")}
            />
          }
        />
      ),
      width: 250,
      dataIndex: "tenDichVu",
      key: "tenDichVu",
      align: "left",
      onCell: (row, index) => ({
        colSpan: row.isGroup ? 7 : 1,
      }),
      render: (item, list) => (
        <span className={list?.isGroup ? "nhom-dv-cap-1" : ""}>{item}</span>
      ),
    },
    {
      title: (
        <HeaderSearch title={t("common.thanhTien")} className="text-center" />
      ),
      width: 110,
      dataIndex: "thanhTien",
      key: "thanhTien",
      align: "right",
      onCell: sharedOnCell,
      render: (item) => formatDecimal(String(item)),
    },
    {
      title: (
        <HeaderSearch title={t("common.soLuong")} className="text-center" />
      ),
      width: 90,
      dataIndex: "soLuong",
      key: "soLuong",
      align: "right",
      onCell: sharedOnCell,
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.phanTramMienGiam")}
          className="text-center"
        />
      ),
      width: 90,
      dataIndex: "phanTramMienGiamDichVu",
      key: "phanTramMienGiamDichVu",
      align: "right",
      onCell: sharedOnCell,
      render: (item) => {
        return item;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.tienMienGiam")}
          className="text-center"
        />
      ),
      width: 110,
      dataIndex: "tienMienGiamDichVu",
      key: "tienMienGiamDichVu",
      align: "right",
      onCell: sharedOnCell,
      render: (item, data) => (
        <InputNumberFormatKpis
          value={item}
          allowNegative={false}
          min={0}
          max={data.thanhTien}
          disabled={
            thongTinPhieuThu.thanhToan ===
              TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN ||
            state?.phanTramMienGiamDichVu > 0 ||
            state?.tienMienGiamDichVu > 0 ||
            state.loaiMienGiam
          }
          onValueChange={(value) => {
            onChangeListData(data)(value);
          }}
        />
      ),
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.maBoChiDinh")}
          search={
            <InputTimeout
              placeholder={t("thuNgan.maBoChiDinh")}
              onChange={onSearchInput("maBoChiDinh")}
            />
          }
        />
      ),
      width: 100,
      dataIndex: "maBoChiDinh",
      key: "maBoChiDinh",
      align: "left",
      onCell: sharedOnCell,
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.tenBoChiDinh")}
          search={
            <InputTimeout
              placeholder={t("thuNgan.tenBoChiDinh")}
              onChange={onSearchInput("tenBoChiDinh")}
            />
          }
        />
      ),
      width: 180,
      dataIndex: "tenBoChiDinh",
      key: "tenBoChiDinh",
      align: "left",
      onCell: sharedOnCell,
    },
  ];

  const rowSelection = {
    columnTitle: (
      <HeaderSearch
        title={
          <Checkbox
            onChange={onSelectAll}
            checked={state.checkAll}
            disabled={
              thongTinPhieuThu.thanhToan ===
              TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN
            }
          />
        }
      />
    ),
    columnWidth: 30,
    onSelect: onSelectChange,
    selectedRowKeys: state.selectedRowKeys,
    onCell: sharedOnCell,
    getCheckboxProps: () => ({
      disabled:
        thongTinPhieuThu.thanhToan ===
        TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN,
    }),
  };

  const renderEmptyTextLeftTable = () => {
    return (
      <div style={{ marginTop: 130 }}>
        <div style={{ color: "#c3c3c3", fontSize: 14 }}>
          {t("common.khongCoDuLieuPhuHop")}
        </div>
      </div>
    );
  };

  return (
    <div className="receipt">
      {/* <div className="item-row text-bold">{t("thuNgan.phanTramMienGiam")}</div> */}
      <div className="item-row">
        <div className="title text-bold">
          {t("thuNgan.dienPhanTramMienGiamApDung")}:
        </div>{" "}
        <div className="num">
          <InputNumberFormat
            style={{ width: "240px" }}
            placeholder={t("thuNgan.nhapSoPhanTram")}
            onChange={onChangeInput("phanTramMienGiamDichVu")}
            disabled={
              thongTinPhieuThu.thanhToan ===
                TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN ||
              state?.tienMienGiamDichVu > 0 ||
              state.loaiMienGiam ||
              state.mienGiamTheoTienCong
            }
            max={100}
            value={state?.phanTramMienGiamDichVu}
          />{" "}
          %
        </div>
      </div>
      <div className="item-row">
        <div className="title text-bold">
          {t("thuNgan.dienSoTienMienGiam")}:
        </div>
        <div className="num">
          <InputNumberFormat
            style={{ width: "240px" }}
            placeholder={t("thuNgan.nhapSoTien")}
            onChange={onChangeInput("tienMienGiamDichVu")}
            disabled={
              thongTinPhieuThu.thanhToan ===
                TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN ||
              state?.phanTramMienGiamDichVu > 0 ||
              state.loaiMienGiam ||
              state.mienGiamTheoTienCong
            }
            value={state?.tienMienGiamDichVu}
          />
        </div>
      </div>
      <div className="item-row">
        <div className="title text-bold">{t("thuNgan.tienMienGiamCttTt")}:</div>
        <div className="num">
          <Select
            data={LIST_MIEN_GIAM}
            value={state?.loaiMienGiam}
            onChange={onChangeSelect}
            disabled={
              thongTinPhieuThu.thanhToan ===
                TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN ||
              state?.phanTramMienGiamDichVu > 0 ||
              state?.tienMienGiamDichVu > 0 ||
              state.mienGiamTheoTienCong
            }
            placeholder={t("danhMuc.chonLoaiMienGiam")}
            style={{ width: "240px" }}
            showArrow
            dropdownMatchSelectWidth={350}
          />
        </div>
      </div>
      <div className="item-row">
        <div className="title text-bold">
          {t("thuNgan.mienGiamTienCongThucHien")}:
        </div>
        <div className="num">
          <Checkbox
            onChange={onChangeCheckbox}
            checked={state.mienGiamTheoTienCong}
            disabled={
              thongTinPhieuThu.thanhToan ===
                TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN ||
              state?.phanTramMienGiamDichVu > 0 ||
              state?.tienMienGiamDichVu > 0 ||
              state.loaiMienGiam
            }
          />
        </div>
      </div>
      <div className="item-row">
        <div className="title text-bold">
          {t("thuNgan.quanLyTamUng.ghiChu")}:
        </div>{" "}
        <div className="num">
          <InputTimeout
            isTextArea={true}
            placeholder={t("thuNgan.nhapNoiDungGhiChu")}
            onChange={(e) => {
              onUpdateReceipt("ghiChu", e?.target?.value);
            }}
            value={ghiChu}
          />
        </div>
      </div>
      <span className="text-bold subtitle">
        {t("thuNgan.chonDichVuDeApDungMienGiam")}
      </span>
      <div className="miengiam-noidung">
        <div className="title-2 text-bolder">
          {t("common.daChon")} {state.listData.length} {t("common.dichVu")}
        </div>
        <TableWrapper
          columns={columns}
          dataSource={listDVKT}
          rowSelection={rowSelection}
          rowKey={(record) => record.key}
          style={{
            marginTop: 0,
          }}
          scroll={{
            y: 200,
            x: 1000,
          }}
          locale={{
            emptyText: renderEmptyTextLeftTable(),
          }}
        />
      </div>
    </div>
  );
};

export default DiscountOnService;
