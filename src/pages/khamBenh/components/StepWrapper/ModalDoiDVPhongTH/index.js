import React, {
  forwardRef,
  useState,
  useImperativeHandle,
  useRef,
  useEffect,
} from "react";
import { Main } from "./styled";
import { useDispatch } from "react-redux";
import { Button, Checkbox, InputTimeout, ModalTemplate } from "components";
import { useTranslation } from "react-i18next";
import ModalDoiPhong from "../ModalDoiPhong";
import ModalDoiDV from "../ModalDoiDV";
import { useConfirm, useStore, useThietLap } from "hooks";
import {
  TRANG_THAI_DICH_VU,
  LOAI_DICH_VU,
  THIET_LAP_CHUNG,
  DOI_TUONG_KCB_BA_DAI_HAN,
  DOI_TUONG_KCB,
} from "constants/index";
import { message } from "antd";
import { useHistory } from "react-router-dom";

const ModalDoiDVPhongTH = (props, ref) => {
  const refModal = useRef(null);
  const refModalDoiPhong = useRef(null);
  const refModalDoiDV = useRef(null);
  const refCallback = useRef(null);
  const { t } = useTranslation();
  const history = useHistory();
  const { themThongTin } = useDispatch().nbKhamBenh;
  const { clearData } = useDispatch().khamBenh;
  const thongTinChiTiet = useStore("khamBenh.thongTinChiTiet");
  const { showConfirm } = useConfirm();
  const [CHAN_TRUNG_PHONG_KHAM] = useThietLap(
    THIET_LAP_CHUNG.CHAN_TRUNG_PHONG_KHAM
  );
  const thongTinNguoiBenh = useStore(
    "chiDinhKhamBenh.configData.thongTinNguoiBenh"
  );
  const [state, _setState] = useState({
    show: false,
    dichVu: {},
    phong: {},
    isEditDichVu: true,
    isEditPhong: true,
    khacNhomDv: false,
  });
  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };
  useImperativeHandle(ref, () => ({
    show: (
      {
        dichVuCu = "",
        tenPhongThucHien = "",
        phongThucHienId = null,
        dichVuCuId,
      },
      callback
    ) => {
      const isEditDichVu =
        thongTinChiTiet?.nbDichVu?.loaiDichVu === LOAI_DICH_VU.KHAM &&
        !thongTinChiTiet?.nbDichVu?.thanhToan;

      const isEditPhong = [
        TRANG_THAI_DICH_VU.CHO_KHAM,
        TRANG_THAI_DICH_VU.CHUAN_BI_KHAM,
        TRANG_THAI_DICH_VU.DANG_KHAM,
        TRANG_THAI_DICH_VU.DANG_THUC_HIEN_DICH_VU,
        TRANG_THAI_DICH_VU.CHO_KET_LUAN,
        TRANG_THAI_DICH_VU.DANG_KET_LUAN,
        TRANG_THAI_DICH_VU.DA_CHECKIN_KET_LUAN,
        TRANG_THAI_DICH_VU.CHUAN_BI_KET_LUAN,
        TRANG_THAI_DICH_VU.BO_QUA_KET_LUAN,
      ].includes(thongTinChiTiet?.nbDvKyThuat?.trangThai);
      setState({
        show: true,
        dichVuCu,
        isEditDichVu,
        isEditPhong,
        dichVu: {},
        dichVuCuId,
        phong: {
          phongId: phongThucHienId,
          ten: tenPhongThucHien,
        },
        phongCuId: phongThucHienId,
        khacNhomDv: false,
      });

      refCallback.current = callback;
    },
  }));

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state.show]);

  const onOK = (isOk) => () => {
    if (isOk) {
      if (state.dichVu?.id && !state.phong?.phongId) {
        message.error(t("baoCao.vuiLongChonPhongThucHien") + "!");
        return;
      }
      if (
        state?.phongCuId === state.phong?.phongId &&
        state?.dichVuCuId === state.dichVu?.id &&
        CHAN_TRUNG_PHONG_KHAM?.eval() &&
        [
          DOI_TUONG_KCB.NGOAI_TRU,
          DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
          DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
        ].includes(thongTinNguoiBenh.doiTuongKcb)
      ) {
        showConfirm({
          title: t("common.thongBao"),
          content: `${t(
            "khamBenh.phongBanVuaChonDaDuocChiDinhTruocDoVuiLongChonPhongKhac",
            { tenPhong: state.phong?.ten }
          )}`,
          cancelText: t("common.huy"),
          classNameOkText: "button-warning",
          typeModal: "warning",
        });
        return;
      }
      let payload = [
        {
          id: thongTinChiTiet?.id,
          nbDichVu: { dichVuId: state.dichVu?.id },
          nbDvKyThuat: { phongThucHienId: state.phong?.phongId },
        },
      ];
      themThongTin(payload).then(() => {
        clearData({});
        setState({ show: false, index: null, currentItem: null });

        if (refCallback.current) {
          refCallback.current();
        } else {
          //reload về bệnh nhân đầu tiên của phòng khám
          setTimeout(() => {
            history.push(`/kham-benh/${state.phongCuId}`);
          }, 500);
        }
      });
    } else {
      setState({
        show: false,
        index: null,
        currentItem: null,
        khacNhomDv: false,
      });
    }
  };

  const onClickPhongTH = () => {
    if (!state.isEditPhong) return;

    refModalDoiPhong.current &&
      refModalDoiPhong.current.show(
        {
          dichVuId: state?.dichVu?.id || thongTinChiTiet?.nbDichVu?.dichVuId,
          khoaChiDinhId: thongTinChiTiet?.nbDichVu?.khoaChiDinhId,
        },
        (value) => {
          setState(value);
        }
      );
  };

  const onClickDichVu = () => {
    if (!state.isEditDichVu) return;
    refModalDoiDV.current &&
      refModalDoiDV.current.show({ khacNhomDv: state.khacNhomDv }, (value) => {
        setState(value);
      });
  };

  return (
    <ModalTemplate
      width={640}
      onCancel={onOK(false)}
      ref={refModal}
      title={t("khamBenh.doiDichVuPhongThucHien")}
      actionLeft={<Button.QuayLai onClick={onOK(false)} />}
      actionRight={
        <Button
          type="primary"
          minWidth={100}
          iconHeight={15}
          onClick={onOK(true)}
          disabled={!state.dichVu?.id && !state.phong?.phongId}
        >
          {t("common.luu")}
        </Button>
      }
      destroyOnClose
    >
      <Main>
        <div className="item">
          <div className="item-label">{t("cdha.dichVuCu")}</div>
          <div className="item-content">
            <InputTimeout
              disabled
              placeholder={t("cdha.dichVuCu")}
              value={state.dichVuCu}
            />
          </div>
        </div>

        <div className="item">
          <div className="item-label">{t("cdha.dichVuMoi")}</div>
          <div className="item-content">
            <InputTimeout
              value={state.dichVu?.ten || ""}
              placeholder={t("cdha.dichVuMoi")}
              onClick={onClickDichVu}
              disabled={!state.isEditDichVu}
            />
          </div>
        </div>

        <div className="item">
          <div className="item-content">
            <Checkbox
              value={state.khacNhomDv}
              onChange={(e) => setState({ khacNhomDv: e.target.checked })}
            >
              {t("khamBenh.doiDichVuKhacNhomDichVu")}
            </Checkbox>
          </div>
        </div>

        <div className="item">
          <div className="item-label">
            {t("khamBenh.chiDinh.phongThucHien")}
          </div>
          <div className="item-content">
            <InputTimeout
              value={state.phong?.ten || ""}
              placeholder={t("danhMuc.chonPhongThucHien")}
              onClick={onClickPhongTH}
              disabled={!state.isEditPhong}
            />
          </div>
        </div>
      </Main>

      <ModalDoiPhong ref={refModalDoiPhong} />
      <ModalDoiDV ref={refModalDoiDV} />
    </ModalTemplate>
  );
};

export default forwardRef(ModalDoiDVPhongTH);
