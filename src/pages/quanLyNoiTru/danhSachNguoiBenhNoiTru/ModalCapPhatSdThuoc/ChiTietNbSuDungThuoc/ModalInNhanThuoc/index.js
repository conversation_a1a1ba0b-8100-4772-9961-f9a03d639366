import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { TimePicker, message } from "antd";
import { useLoading } from "hooks";
import {
  Button,
  InputNumberField,
  InputTimeout,
  ModalTemplate,
} from "components";
import { Main } from "./styled";
import { orderBy, size } from "lodash";

const ModalInNhanThuoc = (props, ref) => {
  const refModal = useRef(null);
  const refCallback = useRef(null);
  const [state, _setState] = useState({
    show: false,
    data: {},
  });
  const setState = (data = {}) => _setState((state) => ({ ...state, ...data }));

  const { t } = useTranslation();

  useImperativeHandle(ref, () => ({
    show: ({ dsThuoc, id }, callback) => {
      console.log("🚀 KhoaMilan -> dsThuoc", dsThuoc);
      setState({
        show: true,
        dsThuoc,
      });

      refCallback.current = callback;
    },
  }));

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state.show]);

  const onClose = () => {
    setState({ show: false });
  };

  const onOk = async () => {};

  return (
    <ModalTemplate
      ref={refModal}
      title={t("quanLyNoiTru.capPhatThuoc.ngungThuoc")}
      width={"min(95vw,500px)"}
      onCancel={onClose}
      actionRight={
        <>
          <Button onClick={onClose}>{t("common.huy")}</Button>
          <Button onClick={onOk} type="primary">
            {t("common.xacNhan")}
          </Button>
        </>
      }
    >
      <Main></Main>
    </ModalTemplate>
  );
};

export default forwardRef(ModalInNhanThuoc);
