import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
  useMemo,
} from "react";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { TreeSelect, Form, message } from "antd";
import { Button, DatePicker, ModalTemplate } from "components";
import { Main } from "./styled";

const ModalInNhanThuoc = (_, ref) => {
  const refModal = useRef(null);
  const refCallback = useRef(null);
  const [form] = Form.useForm();
  const [state, _setState] = useState({
    show: false,
    dsThuoc: [],
    selectedMedications: [],
  });
  const setState = (data = {}) => _setState((state) => ({ ...state, ...data }));

  const { t } = useTranslation();

  useImperativeHandle(ref, () => ({
    show: ({ dsThuoc }, callback) => {
      console.log("🚀 KhoaMilan -> dsThuoc", dsThuoc);
      setState({
        show: true,
        dsThuoc: dsThuoc || [],
        selectedMedications: [],
      });
      form.resetFields();
      refCallback.current = callback;
    },
  }));

  // Transform dsThuoc to tree data structure
  const treeData = useMemo(() => {
    return (state.dsThuoc || []).map((thuoc) => ({
      title: thuoc.tenDichVu || `Thuốc ${thuoc.id}`,
      value: thuoc.id,
      key: thuoc.id,
      loai: thuoc.loai,
      dsThoiGianSuDung: thuoc.dsThoiGianSuDung || [],
    }));
  }, [state.dsThuoc]);

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state.show]);

  const onClose = () => {
    setState({ show: false, selectedMedications: [] });
    form.resetFields();
  };

  const onTreeSelectChange = (selectedValues) => {
    setState({ selectedMedications: selectedValues });
  };

  const onSubmit = async () => {
    try {
      const values = await form.validateFields();
      const { selectedMedications, dateRange } = values;

      if (!selectedMedications || selectedMedications.length === 0) {
        message.error(t("common.vuiLongChonThuoc"));
        return;
      }

      if (!dateRange || !dateRange[0] || !dateRange[1]) {
        message.error(t("common.vuiLongChonKhoangThoiGian"));
        return;
      }

      // Build the submission data structure
      const dsThuoc = selectedMedications.map((medicationId) => {
        const medication = state.dsThuoc.find((m) => m.id === medicationId);
        return {
          id: medicationId,
          loai: medication?.loai || 10,
          dsThoiGianSuDung: [
            {
              tuThoiGian: moment(dateRange[0]).format("YYYY-MM-DD HH:mm:ss"),
              denThoiGian: moment(dateRange[1]).format("YYYY-MM-DD HH:mm:ss"),
            },
          ],
        };
      });

      console.log("🚀 Submission data:", { dsThuoc });

      if (refCallback.current) {
        refCallback.current({ dsThuoc });
      }

      onClose();
    } catch (error) {
      console.error("Form validation failed:", error);
    }
  };

  return (
    <ModalTemplate
      ref={refModal}
      title={t("quanLyNoiTru.capPhatThuoc.inNhanThuoc")}
      width={"min(95vw,600px)"}
      onCancel={onClose}
      actionRight={
        <>
          <Button onClick={onClose}>{t("common.huy")}</Button>
          <Button onClick={onSubmit} type="primary">
            {t("common.xacNhan")}
          </Button>
        </>
      }
    >
      <Main>
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            selectedMedications: [],
            dateRange: null,
          }}
        >
          <Form.Item
            label={t("quanLyNoiTru.capPhatThuoc.chonThuoc")}
            name="selectedMedications"
            rules={[
              {
                required: true,
                message: t("common.vuiLongChonThuoc"),
              },
            ]}
          >
            <TreeSelect
              treeData={treeData}
              treeCheckable={true}
              showCheckedStrategy={TreeSelect.SHOW_PARENT}
              placeholder={t("quanLyNoiTru.capPhatThuoc.chonThuocCanIn")}
              style={{ width: "100%" }}
              onChange={onTreeSelectChange}
              maxTagCount="responsive"
            />
          </Form.Item>

          <Form.Item
            label={t("quanLyNoiTru.capPhatThuoc.khoangThoiGianSuDung")}
            name="dateRange"
            rules={[
              {
                required: true,
                message: t("common.vuiLongChonKhoangThoiGian"),
              },
            ]}
          >
            <DatePicker.RangePicker
              showTime
              format="DD/MM/YYYY HH:mm"
              placeholder={[t("common.tuThoiGian"), t("common.denThoiGian")]}
              style={{ width: "100%" }}
            />
          </Form.Item>
        </Form>
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(ModalInNhanThuoc);
