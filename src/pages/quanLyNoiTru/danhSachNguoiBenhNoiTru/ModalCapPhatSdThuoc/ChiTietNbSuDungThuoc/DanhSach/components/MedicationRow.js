import React from "react";
import classNames from "classnames";
import moment from "moment";
import { useTranslation } from "react-i18next";
import { SwapRightOutlined, PauseCircleOutlined } from "@ant-design/icons";
import { Button, Checkbox, Tooltip } from "components";
import { TRANG_THAI_THUOC, LOAI_CHI_DINH } from "constants/index";
import { useCapPhatThuoc } from "../../../hooks/useCapPhatThuoc";
import { isSameDay } from "../../../config";
import { SVG } from "assets";
import PopoverSl from "../../TableThuoc/PopoverSl";
import StatusBadge from "./StatusBadge";

const MedicationRow = React.memo(
  ({
    data,
    calculateTime,
    rowIndex,
    childrenCount = 0,
    isParent = false,
    patientId,
  }) => {
    const { medication, isChild, group, patient } = data;
    const {
      onClickTenThuoc,
      onChangeThoiGianSdThuoc,
      onEditThoiGian,
      onPauseThoiGianSuDungThuoc,
      onHuySuDungThuoc,
      onHuyBanGiao,
      onNgungSDThuoc,
      onHuyNgungSDThuoc,
      onDieuDuongXacNhan,
      getNhanVienById,
      getTenThuocById,
      isTachDongCapPhatThuoc,
      dataMA_DUONG_DUNG_DICH_TRUYEN,
      checkIsDisabled,
      state,
      onSelectRow,
    } = useCapPhatThuoc();

    const { t } = useTranslation();

    const checkShowNhapThoiGianSuDung = (record) => {
      if (
        record.tenDonViTinh === record.tenDvtSoCap &&
        record.tenDonViTinh !== record.tenDvtSuDung
      ) {
        return true;
      }

      const soLuongTong =
        record?.loai === 60 ? record.soLuong : record.soLuongYeuCau;
      const soLuongDaNhap =
        record.dsThoiGianSuDung?.reduce((acc, item) => acc + item.soLuong, 0) ||
        0;

      return (
        soLuongTong > soLuongDaNhap &&
        (isTachDongCapPhatThuoc && record.tachDong
          ? !record.dsThoiGianSuDung?.length
          : true)
      );
    };

    const getDonVi = ({ tenDonViTinh, tenDvtSoCap, tenDvtSuDung, loai }) => {
      if (
        loai === 60 ||
        (tenDonViTinh === tenDvtSoCap && tenDonViTinh !== tenDvtSuDung)
      ) {
        return tenDonViTinh || "";
      }

      return tenDvtSuDung || "";
    };

    const renderThoiGian = (list, field, time) => {
      let listThoiGian = list.dsThoiGianSuDungConfig?.[time];
      let renderThoiGian = null;
      let renderThoiGianNgungThuoc = null;
      let markColor = false;
      const isThuocDichTruyen = dataMA_DUONG_DUNG_DICH_TRUYEN.includes(
        list.maDuongDung
      );
      const isShowThoiGianBatDau = list.thoiGianBatDau
        ? calculateTime(list.thoiGianBatDau)?.timeLine?.unsignText() === time
        : false;
      // case đã có thời gian nhập
      if (listThoiGian) {
        if ((listThoiGian.data || []).some((i) => !i.dieuDuongId))
          markColor = true;
        if ((listThoiGian.data || []).some((i) => i.dieuDuongId)) {
          markColor = false;
        }
        renderThoiGian = (
          <>
            {(listThoiGian.data || []).map((item, index) => {
              const { renderTime, dieuDuongId } = item;
              let _user = getNhanVienById(dieuDuongId)?.taiKhoan || "";
              let _soLuong =
                !dieuDuongId && index === 0 ? field : item?.soLuong;

              return (
                <div
                  key={index}
                  className="virt-time-usage__item virt-flex virt-flex--center virt-flex--column"
                >
                  <div className="virt-time-usage__time-group virt-flex virt-flex--center">
                    <span
                      onClick={onEditThoiGian({ list, page: patient.page })}
                    >
                      <span className="virt-flex virt-flex--center">
                        <>
                          <div className="virt-time-usage__time">
                            {renderTime.tuThoiGian}
                          </div>
                          {renderTime.denThoiGian &&
                            (renderTime.denThoiGian.type === "arrow" ? (
                              <SwapRightOutlined />
                            ) : renderTime.denThoiGian.type ===
                              "arrowWithTime" ? (
                              <>
                                <SwapRightOutlined />
                                <div className="virt-time-usage__time">
                                  {renderTime.denThoiGian.time}
                                </div>
                              </>
                            ) : null)}
                        </>
                      </span>
                    </span>
                    {!!_soLuong && (
                      <span className="virt-time-usage__quantity">
                        &nbsp; ({_soLuong}&nbsp;
                        {(list?.loai === 60
                          ? list?.tenDonViTinh
                          : list?.tenDvtSuDung) || ""}
                        )
                      </span>
                    )}

                    {!dieuDuongId &&
                      checkShowNhapThoiGianSuDung(list) &&
                      !checkIsDisabled(list) && (
                        <Tooltip
                          title={t(
                            "quanLyNoiTru.capPhatThuoc.nhapThoiGianSuDung"
                          )}
                        >
                          <SVG.IcTiepTuc
                            className="virt-cursor--pointer"
                            onClick={onChangeThoiGianSdThuoc({
                              item,
                              data: list,
                              page: medication.page,
                              defaultTime:
                                markColor && isShowThoiGianBatDau
                                  ? moment().format("YYYY-MM-DD") +
                                    moment(list.thoiGianBatDau).format(
                                      " HH:mm:ss"
                                    )
                                  : null,
                            })}
                          />
                        </Tooltip>
                      )}
                    {/* Hiển thị thời gian bắt đầu theo bác sĩ kê */}
                    {/* Nếu chưa có nhập thời gian tại buổi này */}
                    {markColor && isShowThoiGianBatDau && (
                      <div className="virt-time-usage__time-start">
                        {moment(list.thoiGianBatDau).format("HH:mm")}
                      </div>
                    )}
                    {dieuDuongId &&
                      isThuocDichTruyen &&
                      !item.denThoiGian &&
                      item.tuThoiGian &&
                      !checkIsDisabled(list) && (
                        <Tooltip
                          title={t(
                            "quanLyNoiTru.capPhatThuoc.nhapThoiGianKetThuc"
                          )}
                        >
                          <div className="virt-icon-pause">
                            <PauseCircleOutlined
                              onClick={onPauseThoiGianSuDungThuoc({
                                item,
                                list,
                                page: patient.page,
                              })}
                            />
                          </div>
                        </Tooltip>
                      )}
                  </div>
                  <p className="virt-time-usage__user">
                    {_user ? `(${_user})` : ""}
                  </p>
                </div>
              );
            })}
          </>
        );
      } else if (field) {
        // case có thời gian sáng chiều tối
        markColor = true;
        renderThoiGian = (
          <div>
            <div className="flex flex-center flex-wrap gap-4">
              <span className="flex flex-center">
                ({field}
                {(list?.loai === 60
                  ? list?.tenDonViTinh
                  : list?.tenDvtSuDung) || ""}
                )
              </span>
              {isShowThoiGianBatDau && (
                <span className="virt-time-usage__time-start">
                  {moment(list.thoiGianBatDau).format("HH:mm")}
                </span>
              )}
              {!checkIsDisabled(list) && (
                <Tooltip
                  title={t("quanLyNoiTru.capPhatThuoc.nhapThoiGianSuDung")}
                >
                  <SVG.IcTiepTuc
                    style={{
                      cursor: "pointer",
                    }}
                    onClick={onChangeThoiGianSdThuoc({
                      data: list,
                      page: patient.page,
                      defaultTime:
                        markColor && isShowThoiGianBatDau
                          ? moment().format("YYYY-MM-DD") +
                            moment(list.thoiGianBatDau).format(" HH:mm:ss")
                          : null,
                    })}
                  />
                </Tooltip>
              )}
            </div>
          </div>
        );
      }
      if (list.thoiGianNgungSd) {
        let { timeLine, time: thoiGianNgungSd } = calculateTime(
          list.thoiGianNgungSd
        );
        if (timeLine.unsignText() === time)
          renderThoiGianNgungThuoc = (
            <p className="m-0 text-danger">
              {`${
                isSameDay(
                  moment(list.thoiGianNgungSd),
                  moment(list.thoiGianThucHien)
                )
                  ? thoiGianNgungSd
                  : moment(list.thoiGianNgungSd).format("HH:mm DD/MM")
              } ${t("quanLyNoiTru.capPhatThuoc.ngungSD")} ${
                list.soLuongTra || ""
              } 
            ${getDonVi(list)}
            ${list.lyDoNgungSd ? `, ${list.lyDoNgungSd}` : ""}`}
            </p>
          );
      }

      return (
        <div
          className={classNames("virt-time-usage", {
            "virt-time-usage--merged": listThoiGian?.isMerge,
            "virt-time-usage--highlighted": markColor,
          })}
        >
          <div className="virt-time-usage__item">{renderThoiGian}</div>
          {renderThoiGianNgungThuoc && (
            <div className="virt-time-usage__stop-notice">
              {renderThoiGianNgungThuoc}
            </div>
          )}
        </div>
      );
    };

    const renderDrugInfo = () => {
      const {
        hamLuong,
        soLuongHuy,
        soLuongTra,
        lyDoHuy,
        tenHoatChat,
        tenDichVu,
        sttNgaySuDung,
        ghiChu,
        cachDung,
        tenLieuDung,
        ghiChuCapPhatThuoc,
        loaiChiDinh,
      } = medication;

      const donVi = getDonVi(medication);

      const _slHuy =
        !!soLuongHuy &&
        ` (${t("common.huy")} ${soLuongHuy} ${donVi}${
          lyDoHuy ? ` ${lyDoHuy}` : ""
        }${!!soLuongTra ? "" : `)`}`;
      const _slTra = !!soLuongTra
        ? `${!!soLuongHuy ? `, ` : ` (`}${t(
            "quanLyNoiTru.tra"
          )} ${soLuongTra} ${donVi})`
        : "";

      return (
        <div className="virt-drug-info">
          <div className="virt-drug-info__container">
            <div className="virt-drug-info__name">
              {!!sttNgaySuDung && (
                <div className="virt-drug-info__circle">{sttNgaySuDung}</div>
              )}{" "}
              <span>
                {tenDichVu}
                {tenHoatChat || hamLuong
                  ? tenHoatChat
                    ? hamLuong
                      ? ` (${tenHoatChat} - ${hamLuong})`
                      : ` (${tenHoatChat})`
                    : `(-${hamLuong})`
                  : ""}
                {loaiChiDinh === LOAI_CHI_DINH.DOT_XUAT && (
                  <span className="virt-status-badge virt-status-badge--dot-xuat">
                    {t("kho.dotXuat")}
                  </span>
                )}
                {loaiChiDinh === LOAI_CHI_DINH.BO_SUNG && (
                  <span className="virt-status-badge virt-status-badge--bo-sung">
                    {t("kho.boSung")}
                  </span>
                )}
              </span>
              <span className="virt-drug-info__cancelled">
                {_slHuy}
                {_slTra}
              </span>
            </div>

            {(cachDung || tenLieuDung || ghiChu) && (
              <ul className="virt-drug-info__details">
                <li>
                  {tenLieuDung} {cachDung}
                  {(tenLieuDung || cachDung) && <>. </>}
                  {ghiChu && (
                    <span className="virt-text--italic">
                      {t("common.ghiChu")}: {ghiChu}
                    </span>
                  )}
                </li>
              </ul>
            )}
            <i className="virt-drug-info__notes">{ghiChuCapPhatThuoc || ""}</i>
          </div>
        </div>
      );
    };

    const renderConfirmation = () => {
      if (!isTachDongCapPhatThuoc || checkIsDisabled(medication)) return null;

      return (
        <div className="flex-center">
          {medication.dieuDuongXacNhan ? (
            <>
              {medication.dieuDuongXacNhan?.taiKhoan}
              <SVG.IcDelete
                onClick={onDieuDuongXacNhan({
                  list: medication,
                  page: patient.page,
                })}
                style={{ marginLeft: "5px" }}
                className="red"
              />
            </>
          ) : medication.tachDong ? (
            <Button
              onClick={onDieuDuongXacNhan({
                list: medication,
                page: patient.page,
              })}
            >
              {t("common.xacNhan")}
            </Button>
          ) : null}
        </div>
      );
    };

    const renderActions = () => {
      if (checkIsDisabled(medication)) return null;
      return (
        <>
          {checkShowNhapThoiGianSuDung(medication) && (
            <Tooltip title={t("quanLyNoiTru.capPhatThuoc.nhapThoiGianSuDung")}>
              <SVG.IcTiepTuc
                className="cursor-pointer"
                onClick={onChangeThoiGianSdThuoc({
                  data: medication,
                  page: patient.page,
                })}
              />
            </Tooltip>
          )}
          {medication.trangThai === TRANG_THAI_THUOC.DA_SU_DUNG.id && (
            <Tooltip
              title={t("quanLyNoiTru.capPhatThuoc.huyXacNhanSuDungThuoc")}
              placement="left"
            >
              <SVG.IcCloseCircle
                className="cursor-pointer"
                color={"var(--color-red-primary)"}
                onClick={onHuySuDungThuoc({
                  data: medication,
                  page: patient.page,
                })}
              />
            </Tooltip>
          )}
          {medication.trangThai === TRANG_THAI_THUOC.DA_BAN_GIAO.id && (
            <Tooltip title={t("quanLyNoiTru.huyBanGiao")}>
              <SVG.IcDonThuoc
                className="cursor-pointer"
                color={"var(--color-red-primary)"}
                onClick={onHuyBanGiao({ data: medication, page: patient.page })}
              />
            </Tooltip>
          )}
          {[TRANG_THAI_THUOC.DA_SU_DUNG.id].includes(medication.trangThai) && (
            <Tooltip
              title={t("quanLyNoiTru.capPhatThuoc.ngungSDThuoc")}
              placement="left"
            >
              <SVG.IcNgungThuoc
                className="cursor-pointer"
                onClick={onNgungSDThuoc({
                  data: medication,
                  page: patient.page,
                })}
              />
            </Tooltip>
          )}
          {[TRANG_THAI_THUOC.NGUNG_SU_DUNG.id].includes(
            medication.trangThai
          ) && (
            <Tooltip
              title={t("quanLyNoiTru.huyNgungSuDungThuoc")}
              placement="left"
            >
              <SVG.IcDonThuoc
                className="cursor-pointer"
                onClick={onHuyNgungSDThuoc({
                  data: medication,
                  page: patient.page,
                })}
                color={"var(--color-green-primary)"}
              />
            </Tooltip>
          )}
        </>
      );
    };

    const groupKey = `${patientId}-${group?.id}`;

    return (
      <div
        className={classNames("virt-medication-row", {
          "virt-medication-row--child": isChild,
          "virt-medication-row--parent-with-children":
            isParent && childrenCount > 0,
          "virt-medication-row--even": rowIndex % 2 === 0,
          "virt-medication-row--odd": rowIndex % 2 === 1,
          "virt-medication-row--with-confirmation": isTachDongCapPhatThuoc,
        })}
        style={{
          "--child-count": childrenCount + 1, // Include parent in count
          "--action-grid-col": isTachDongCapPhatThuoc ? "12" : "11",
        }}
      >
        {/* Checkbox - Grid Column 1 */}
        <div className="virt-medication-row__cell virt-medication-row__cell--checkbox">
          {!isChild && (
            <Checkbox
              checked={(state.selectedRowKeysByGroup[groupKey] || []).includes(
                medication?.id
              )}
              onChange={onSelectRow(groupKey, medication?.id)}
            />
          )}
        </div>

        {/* STT - Grid Column 2 */}
        <div className="virt-medication-row__cell virt-medication-row__cell--stt">
          {!isChild ? medication.index : ""}
        </div>

        {/* Drug Info - Grid Column 3 */}
        <div
          className="virt-medication-row__cell virt-medication-row__cell--drug-info"
          onClick={(e) =>
            onClickTenThuoc({ record: medication, page: patient.page })
          }
        >
          {renderDrugInfo()}
        </div>

        {/* Quantity - Grid Column 4 */}
        <div className="virt-medication-row__cell virt-medication-row__cell--quantity">
          <div className="virt-quantity-info">
            <PopoverSl
              record={medication}
              page={patient.page}
              trangThaiNb={patient.trangThai}
            />
          </div>
        </div>

        {/* Unit - Grid Column 5 */}
        <div className="virt-medication-row__cell virt-medication-row__cell--unit">
          {getDonVi(medication)}
        </div>

        {/* Time schedules - Grid Columns 6-9 */}
        <div
          className="virt-medication-row__cell virt-medication-row__cell--time-use"
          data-time="sang"
        >
          {renderThoiGian(medication, medication.slSang, "Sang")}
        </div>
        <div
          className="virt-medication-row__cell virt-medication-row__cell--time-use"
          data-time="chieu"
        >
          {renderThoiGian(medication, medication.slChieu, "Chieu")}
        </div>
        <div
          className="virt-medication-row__cell virt-medication-row__cell--time-use"
          data-time="toi"
        >
          {renderThoiGian(medication, medication.slToi, "Toi")}
        </div>
        <div
          className="virt-medication-row__cell virt-medication-row__cell--time-use"
          data-time="dem"
        >
          {renderThoiGian(medication, medication.slDem, "Dem")}
        </div>

        {/* Status - Grid Column 10/11 (merged for children) */}

        <div className="virt-medication-row__cell virt-medication-row__cell--status">
          <StatusBadge status={medication.trangThai}>
            {getTenThuocById(medication.trangThai)}
          </StatusBadge>
        </div>

        {/* Confirmation - Grid Column 11 (conditional, merged for children) */}
        {isTachDongCapPhatThuoc && !isChild && (
          <div className="virt-medication-row__cell virt-medication-row__cell--confirmation">
            {renderConfirmation()}
          </div>
        )}

        {/* Actions - Grid Column 11/12 (merged for children) */}
        {!isChild && (
          <div className="virt-medication-row__cell virt-medication-row__cell--actions">
            <div className="virt-action-icons">{renderActions()}</div>
          </div>
        )}
      </div>
    );
  }
);

MedicationRow.displayName = "MedicationRow";

export default MedicationRow;
