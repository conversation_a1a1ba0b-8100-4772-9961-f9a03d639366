import React from "react";
import classNames from "classnames";
import { Dropdown, Tooltip } from "antd";
import { useTranslation } from "react-i18next";
import { SVG } from "assets";
import { useCapPhatThuoc } from "../../../hooks/useCapPhatThuoc";
import { POPUP_TYPE, EVENT } from "../../../config";
import DienBien from "../../TableThuoc/DienBien";
import { Checkbox } from "components";
// Removed flexConfig import - now using CSS Grid

// Component for group header - Now using optimized CSS classes
const GroupHeader = React.memo(
  ({
    group,
    onToggle,
    contentPrint,
    renderTxtBuoi,
    isExpandedGroup,
    patientId,
  }) => {
    const {
      onBanGiaoThuoc,
      setState: setParentState,
      setModalState,
      modalState,
      isTachDongCapPhatThuoc,
      checkDisabledList,
      state,
      onSelectAllGroup,
    } = useCapPhatThuoc();

    const { t } = useTranslation();

    const allData = group?.data || [];

    let groupKey = `${patientId}-${group.id}`;

    return (
      <div className="virt-group-header">
        <div className="virt-group-header__title">
          <div className="virt-group-header__title-left">
            <div className="virt-group-header__title-left-text">
              <button
                className={classNames("virt-expand-icon", {
                  "virt-expand-icon--collapsed": !isExpandedGroup,
                })}
                onClick={onToggle}
              ></button>
            </div>
          </div>
          <div className="virt-group-header__title-right">
            <div className="virt-group-header__title-right-content">
              <div className="virt-text--bold">{group.title}</div>
              <Dropdown
                overlay={contentPrint({
                  id: group.data?.[0]?.nbDotDieuTriId,
                  thoiGianThucHien: group.data?.[0]?.thoiGianThucHien,
                  selectedIds: state.selectedRowKeysByGroup[groupKey],
                  allThuoc: allData,
                  listData: group.data,
                })}
                trigger={["click"]}
              >
                <SVG.IcPrint className="ic-print virt-cursor--pointer" />
              </Dropdown>
            </div>

            <div className="virt-group-header__title-right-actions">
              {!checkDisabledList(group.data) && (
                <>
                  <Tooltip
                    placement="left"
                    title={t("quanLyNoiTru.capPhatThuoc.banGiaoThuoc")}
                  >
                    <SVG.IcBanGiaoThuoc
                      className="virt-group-actions__icon virt-cursor--pointer"
                      onClick={onBanGiaoThuoc({
                        data: group.data,
                        event: EVENT.BAN_GIAO,
                        page: group.page,
                      })}
                    />
                  </Tooltip>
                  <Tooltip
                    placement="left"
                    title={t("quanLyNoiTru.capPhatThuoc.xacNhanSDThuoc")}
                  >
                    <SVG.IcSuccess
                      className="virt-group-actions__icon virt-cursor--pointer"
                      onClick={onBanGiaoThuoc({
                        data: group.data,
                        event: EVENT.SU_DUNG,
                        page: group.page,
                      })}
                    />
                  </Tooltip>
                </>
              )}
              <Tooltip
                placement="left"
                title={t("quanLyNoiTru.capPhatThuoc.danhSachThuocTra")}
              >
                <SVG.IcRectangular
                  color="#ffffff"
                  className="virt-group-actions__icon virt-group-actions__icon--danger virt-cursor--pointer"
                  onClick={() => {
                    setParentState({
                      nbDotDieuTriId: group.data?.[0]?.nbDotDieuTriId,
                    });
                    setModalState({
                      popupType: POPUP_TYPE.DS_HANG_HOA_TRA,
                      previousPopupType: modalState.popupType,
                    });
                  }}
                />
              </Tooltip>
            </div>
          </div>
        </div>

        {/* Table Header - CSS Grid Layout */}
        {isExpandedGroup && (
          <>
            <DienBien data={group.data} />

            <div
              className={classNames("virt-table-header", {
                "virt-table-header--with-confirmation": isTachDongCapPhatThuoc,
              })}
            >
              {/* Grid Column 1 - Checkbox */}
              <div className="virt-table-header__cell virt-table-header__cell--checkbox">
                <Checkbox
                  indeterminate={
                    (state.selectedRowKeysByGroup[groupKey] || []).length > 0 &&
                    (state.selectedRowKeysByGroup[groupKey] || []).length <
                      allData.length
                  }
                  checked={
                    (state.selectedRowKeysByGroup[groupKey] || []).length ===
                      allData.length && allData.length > 0
                  }
                  onChange={(e) =>
                    onSelectAllGroup(groupKey, group.data, e.target.checked)
                  }
                />
              </div>

              {/* Grid Column 2 - STT */}

              <div className="virt-table-header__cell virt-table-header__cell--stt">
                {t("common.stt")}
              </div>

              {/* Grid Column 3 - Drug Info */}
              <div className="virt-table-header__cell virt-table-header__cell--drug-info">
                {t("quanLyNoiTru.toDieuTri.tenThuocLieuDungGhiChu")}
              </div>

              {/* Grid Column 4 - Quantity */}
              <div className="virt-table-header__cell virt-table-header__cell--quantity">
                {t("quanLyNoiTru.suatAn.sl")}
              </div>

              {/* Grid Column 5 - Unit */}
              <div className="virt-table-header__cell virt-table-header__cell--unit">
                {t("quanLyNoiTru.toDieuTri.donVi")}
              </div>

              {/* Grid Columns 6-9 - Time slots */}
              <div className="virt-table-header__cell virt-table-header__cell--time-use">
                {renderTxtBuoi("sang")}
              </div>
              <div className="virt-table-header__cell virt-table-header__cell--time-use">
                {renderTxtBuoi("chieu")}
              </div>
              <div className="virt-table-header__cell virt-table-header__cell--time-use">
                {renderTxtBuoi("toi")}
              </div>
              <div className="virt-table-header__cell virt-table-header__cell--time-use">
                {renderTxtBuoi("dem")}
              </div>

              {/* Grid Column 10/11 - Status */}
              <div className="virt-table-header__cell virt-table-header__cell--status">
                {t("quanLyNoiTru.suatAn.trangThai")}
              </div>

              {/* Grid Column 11 - Confirmation (conditional) */}
              {isTachDongCapPhatThuoc && (
                <div className="virt-table-header__cell virt-table-header__cell--confirmation">
                  {t("kho.tuVanThuoc.nguoiXacNhan")}
                </div>
              )}

              {/* Grid Column 11/12 - Actions */}
              <div className="virt-table-header__cell virt-table-header__cell--actions">
                {t("common.tienIch")}
              </div>
            </div>
          </>
        )}
      </div>
    );
  }
);

GroupHeader.displayName = "GroupHeader";

export default GroupHeader;
